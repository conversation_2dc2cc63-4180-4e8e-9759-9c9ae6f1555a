{"name": "@rollup/plugin-typescript", "version": "11.1.6", "publishConfig": {"access": "public"}, "description": "Seamless integration between Rollup and TypeScript.", "license": "MIT", "repository": {"url": "rollup/plugins", "directory": "packages/typescript"}, "author": "<PERSON><PERSON>", "homepage": "https://github.com/rollup/plugins/tree/master/packages/typescript/#readme", "bugs": "https://github.com/rollup/plugins/issues", "main": "./dist/cjs/index.js", "module": "./dist/es/index.js", "exports": {"types": "./types/index.d.ts", "import": "./dist/es/index.js", "default": "./dist/cjs/index.js"}, "engines": {"node": ">=14.0.0"}, "files": ["dist", "!dist/**/*.map", "types", "README.md", "LICENSE"], "keywords": ["rollup", "plugin", "typescript", "es2015"], "peerDependencies": {"rollup": "^2.14.0||^3.0.0||^4.0.0", "tslib": "*", "typescript": ">=3.7.0"}, "peerDependenciesMeta": {"rollup": {"optional": true}, "tslib": {"optional": true}}, "dependencies": {"@rollup/pluginutils": "^5.1.0", "resolve": "^1.22.1"}, "devDependencies": {"@rollup/plugin-buble": "^1.0.0", "@rollup/plugin-commonjs": "^23.0.0", "@types/node": "^14.18.30", "@types/resolve": "^1.20.2", "buble": "^0.20.0", "rollup": "^4.0.0-24", "typescript": "^4.8.3"}, "types": "./types/index.d.ts", "scripts": {"build": "rollup -c", "ci:coverage": "nyc pnpm test && nyc report --reporter=text-lcov > coverage.lcov", "ci:lint": "pnpm build && pnpm lint", "ci:lint:commits": "commitlint --from=${CIRCLE_BRANCH} --to=${CIRCLE_SHA1}", "ci:test": "pnpm test -- --verbose --serial", "prebuild": "del-cli dist", "prerelease": "pnpm build", "pretest": "pnpm build", "release": "pnpm --workspace-root package:release $(pwd)", "test": "ava", "test:ts": "tsc --noEmit"}}