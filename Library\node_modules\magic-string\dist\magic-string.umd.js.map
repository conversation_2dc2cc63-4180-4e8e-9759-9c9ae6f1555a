{"version": 3, "file": "magic-string.umd.js", "sources": ["../src/BitSet.js", "../src/Chunk.js", "../node_modules/.pnpm/@jridgewell+sourcemap-codec@1.5.0/node_modules/@jridgewell/sourcemap-codec/dist/sourcemap-codec.mjs", "../src/SourceMap.js", "../src/utils/guessIndent.js", "../src/utils/getRelativePath.js", "../src/utils/isObject.js", "../src/utils/getLocator.js", "../src/utils/Mappings.js", "../src/MagicString.js", "../src/Bundle.js", "../src/index-legacy.js"], "sourcesContent": ["export default class BitSet {\n\tconstructor(arg) {\n\t\tthis.bits = arg instanceof BitSet ? arg.bits.slice() : [];\n\t}\n\n\tadd(n) {\n\t\tthis.bits[n >> 5] |= 1 << (n & 31);\n\t}\n\n\thas(n) {\n\t\treturn !!(this.bits[n >> 5] & (1 << (n & 31)));\n\t}\n}\n", "export default class Chunk {\n\tconstructor(start, end, content) {\n\t\tthis.start = start;\n\t\tthis.end = end;\n\t\tthis.original = content;\n\n\t\tthis.intro = '';\n\t\tthis.outro = '';\n\n\t\tthis.content = content;\n\t\tthis.storeName = false;\n\t\tthis.edited = false;\n\n\t\tif (DEBUG) {\n\t\t\t// we make these non-enumerable, for sanity while debugging\n\t\t\tObject.defineProperties(this, {\n\t\t\t\tprevious: { writable: true, value: null },\n\t\t\t\tnext: { writable: true, value: null },\n\t\t\t});\n\t\t} else {\n\t\t\tthis.previous = null;\n\t\t\tthis.next = null;\n\t\t}\n\t}\n\n\tappendLeft(content) {\n\t\tthis.outro += content;\n\t}\n\n\tappendRight(content) {\n\t\tthis.intro = this.intro + content;\n\t}\n\n\tclone() {\n\t\tconst chunk = new Chunk(this.start, this.end, this.original);\n\n\t\tchunk.intro = this.intro;\n\t\tchunk.outro = this.outro;\n\t\tchunk.content = this.content;\n\t\tchunk.storeName = this.storeName;\n\t\tchunk.edited = this.edited;\n\n\t\treturn chunk;\n\t}\n\n\tcontains(index) {\n\t\treturn this.start < index && index < this.end;\n\t}\n\n\teachNext(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.next;\n\t\t}\n\t}\n\n\teachPrevious(fn) {\n\t\tlet chunk = this;\n\t\twhile (chunk) {\n\t\t\tfn(chunk);\n\t\t\tchunk = chunk.previous;\n\t\t}\n\t}\n\n\tedit(content, storeName, contentOnly) {\n\t\tthis.content = content;\n\t\tif (!contentOnly) {\n\t\t\tthis.intro = '';\n\t\t\tthis.outro = '';\n\t\t}\n\t\tthis.storeName = storeName;\n\n\t\tthis.edited = true;\n\n\t\treturn this;\n\t}\n\n\tprependLeft(content) {\n\t\tthis.outro = content + this.outro;\n\t}\n\n\tprependRight(content) {\n\t\tthis.intro = content + this.intro;\n\t}\n\n\treset() {\n\t\tthis.intro = '';\n\t\tthis.outro = '';\n\t\tif (this.edited) {\n\t\t\tthis.content = this.original;\n\t\t\tthis.storeName = false;\n\t\t\tthis.edited = false;\n\t\t}\n\t}\n\n\tsplit(index) {\n\t\tconst sliceIndex = index - this.start;\n\n\t\tconst originalBefore = this.original.slice(0, sliceIndex);\n\t\tconst originalAfter = this.original.slice(sliceIndex);\n\n\t\tthis.original = originalBefore;\n\n\t\tconst newChunk = new Chunk(index, this.end, originalAfter);\n\t\tnewChunk.outro = this.outro;\n\t\tthis.outro = '';\n\n\t\tthis.end = index;\n\n\t\tif (this.edited) {\n\t\t\t// after split we should save the edit content record into the correct chunk\n\t\t\t// to make sure sourcemap correct\n\t\t\t// For example:\n\t\t\t// '  test'.trim()\n\t\t\t//     split   -> '  ' + 'test'\n\t\t\t//   ✔️ edit    -> '' + 'test'\n\t\t\t//   ✖️ edit    -> 'test' + ''\n\t\t\t// TODO is this block necessary?...\n\t\t\tnewChunk.edit('', false);\n\t\t\tthis.content = '';\n\t\t} else {\n\t\t\tthis.content = originalBefore;\n\t\t}\n\n\t\tnewChunk.next = this.next;\n\t\tif (newChunk.next) newChunk.next.previous = newChunk;\n\t\tnewChunk.previous = this;\n\t\tthis.next = newChunk;\n\n\t\treturn newChunk;\n\t}\n\n\ttoString() {\n\t\treturn this.intro + this.content + this.outro;\n\t}\n\n\ttrimEnd(rx) {\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tthis.split(this.start + trimmed.length).edit('', undefined, true);\n\t\t\t\tif (this.edited) {\n\t\t\t\t\t// save the change, if it has been edited\n\t\t\t\t\tthis.edit(trimmed, this.storeName, true);\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn true;\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\tif (this.intro.length) return true;\n\t\t}\n\t}\n\n\ttrimStart(rx) {\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tconst trimmed = this.content.replace(rx, '');\n\n\t\tif (trimmed.length) {\n\t\t\tif (trimmed !== this.content) {\n\t\t\t\tconst newChunk = this.split(this.end - trimmed.length);\n\t\t\t\tif (this.edited) {\n\t\t\t\t\t// save the change, if it has been edited\n\t\t\t\t\tnewChunk.edit(trimmed, this.storeName, true);\n\t\t\t\t}\n\t\t\t\tthis.edit('', undefined, true);\n\t\t\t}\n\t\t\treturn true;\n\t\t} else {\n\t\t\tthis.edit('', undefined, true);\n\n\t\t\tthis.outro = this.outro.replace(rx, '');\n\t\t\tif (this.outro.length) return true;\n\t\t}\n\t}\n}\n", "const comma = ','.charCodeAt(0);\nconst semicolon = ';'.charCodeAt(0);\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\nconst intToChar = new Uint8Array(64); // 64 possible chars.\nconst charToInt = new Uint8Array(128); // z is 122 in ASCII\nfor (let i = 0; i < chars.length; i++) {\n    const c = chars.charCodeAt(i);\n    intToChar[i] = c;\n    charToInt[c] = i;\n}\nfunction decodeInteger(reader, relative) {\n    let value = 0;\n    let shift = 0;\n    let integer = 0;\n    do {\n        const c = reader.next();\n        integer = charToInt[c];\n        value |= (integer & 31) << shift;\n        shift += 5;\n    } while (integer & 32);\n    const shouldNegate = value & 1;\n    value >>>= 1;\n    if (shouldNegate) {\n        value = -0x80000000 | -value;\n    }\n    return relative + value;\n}\nfunction encodeInteger(builder, num, relative) {\n    let delta = num - relative;\n    delta = delta < 0 ? (-delta << 1) | 1 : delta << 1;\n    do {\n        let clamped = delta & 0b011111;\n        delta >>>= 5;\n        if (delta > 0)\n            clamped |= 0b100000;\n        builder.write(intToChar[clamped]);\n    } while (delta > 0);\n    return num;\n}\nfunction hasMoreVlq(reader, max) {\n    if (reader.pos >= max)\n        return false;\n    return reader.peek() !== comma;\n}\n\nconst bufLength = 1024 * 16;\n// Provide a fallback for older environments.\nconst td = typeof TextDecoder !== 'undefined'\n    ? /* #__PURE__ */ new TextDecoder()\n    : typeof Buffer !== 'undefined'\n        ? {\n            decode(buf) {\n                const out = Buffer.from(buf.buffer, buf.byteOffset, buf.byteLength);\n                return out.toString();\n            },\n        }\n        : {\n            decode(buf) {\n                let out = '';\n                for (let i = 0; i < buf.length; i++) {\n                    out += String.fromCharCode(buf[i]);\n                }\n                return out;\n            },\n        };\nclass StringWriter {\n    constructor() {\n        this.pos = 0;\n        this.out = '';\n        this.buffer = new Uint8Array(bufLength);\n    }\n    write(v) {\n        const { buffer } = this;\n        buffer[this.pos++] = v;\n        if (this.pos === bufLength) {\n            this.out += td.decode(buffer);\n            this.pos = 0;\n        }\n    }\n    flush() {\n        const { buffer, out, pos } = this;\n        return pos > 0 ? out + td.decode(buffer.subarray(0, pos)) : out;\n    }\n}\nclass StringReader {\n    constructor(buffer) {\n        this.pos = 0;\n        this.buffer = buffer;\n    }\n    next() {\n        return this.buffer.charCodeAt(this.pos++);\n    }\n    peek() {\n        return this.buffer.charCodeAt(this.pos);\n    }\n    indexOf(char) {\n        const { buffer, pos } = this;\n        const idx = buffer.indexOf(char, pos);\n        return idx === -1 ? buffer.length : idx;\n    }\n}\n\nconst EMPTY = [];\nfunction decodeOriginalScopes(input) {\n    const { length } = input;\n    const reader = new StringReader(input);\n    const scopes = [];\n    const stack = [];\n    let line = 0;\n    for (; reader.pos < length; reader.pos++) {\n        line = decodeInteger(reader, line);\n        const column = decodeInteger(reader, 0);\n        if (!hasMoreVlq(reader, length)) {\n            const last = stack.pop();\n            last[2] = line;\n            last[3] = column;\n            continue;\n        }\n        const kind = decodeInteger(reader, 0);\n        const fields = decodeInteger(reader, 0);\n        const hasName = fields & 0b0001;\n        const scope = (hasName ? [line, column, 0, 0, kind, decodeInteger(reader, 0)] : [line, column, 0, 0, kind]);\n        let vars = EMPTY;\n        if (hasMoreVlq(reader, length)) {\n            vars = [];\n            do {\n                const varsIndex = decodeInteger(reader, 0);\n                vars.push(varsIndex);\n            } while (hasMoreVlq(reader, length));\n        }\n        scope.vars = vars;\n        scopes.push(scope);\n        stack.push(scope);\n    }\n    return scopes;\n}\nfunction encodeOriginalScopes(scopes) {\n    const writer = new StringWriter();\n    for (let i = 0; i < scopes.length;) {\n        i = _encodeOriginalScopes(scopes, i, writer, [0]);\n    }\n    return writer.flush();\n}\nfunction _encodeOriginalScopes(scopes, index, writer, state) {\n    const scope = scopes[index];\n    const { 0: startLine, 1: startColumn, 2: endLine, 3: endColumn, 4: kind, vars } = scope;\n    if (index > 0)\n        writer.write(comma);\n    state[0] = encodeInteger(writer, startLine, state[0]);\n    encodeInteger(writer, startColumn, 0);\n    encodeInteger(writer, kind, 0);\n    const fields = scope.length === 6 ? 0b0001 : 0;\n    encodeInteger(writer, fields, 0);\n    if (scope.length === 6)\n        encodeInteger(writer, scope[5], 0);\n    for (const v of vars) {\n        encodeInteger(writer, v, 0);\n    }\n    for (index++; index < scopes.length;) {\n        const next = scopes[index];\n        const { 0: l, 1: c } = next;\n        if (l > endLine || (l === endLine && c >= endColumn)) {\n            break;\n        }\n        index = _encodeOriginalScopes(scopes, index, writer, state);\n    }\n    writer.write(comma);\n    state[0] = encodeInteger(writer, endLine, state[0]);\n    encodeInteger(writer, endColumn, 0);\n    return index;\n}\nfunction decodeGeneratedRanges(input) {\n    const { length } = input;\n    const reader = new StringReader(input);\n    const ranges = [];\n    const stack = [];\n    let genLine = 0;\n    let definitionSourcesIndex = 0;\n    let definitionScopeIndex = 0;\n    let callsiteSourcesIndex = 0;\n    let callsiteLine = 0;\n    let callsiteColumn = 0;\n    let bindingLine = 0;\n    let bindingColumn = 0;\n    do {\n        const semi = reader.indexOf(';');\n        let genColumn = 0;\n        for (; reader.pos < semi; reader.pos++) {\n            genColumn = decodeInteger(reader, genColumn);\n            if (!hasMoreVlq(reader, semi)) {\n                const last = stack.pop();\n                last[2] = genLine;\n                last[3] = genColumn;\n                continue;\n            }\n            const fields = decodeInteger(reader, 0);\n            const hasDefinition = fields & 0b0001;\n            const hasCallsite = fields & 0b0010;\n            const hasScope = fields & 0b0100;\n            let callsite = null;\n            let bindings = EMPTY;\n            let range;\n            if (hasDefinition) {\n                const defSourcesIndex = decodeInteger(reader, definitionSourcesIndex);\n                definitionScopeIndex = decodeInteger(reader, definitionSourcesIndex === defSourcesIndex ? definitionScopeIndex : 0);\n                definitionSourcesIndex = defSourcesIndex;\n                range = [genLine, genColumn, 0, 0, defSourcesIndex, definitionScopeIndex];\n            }\n            else {\n                range = [genLine, genColumn, 0, 0];\n            }\n            range.isScope = !!hasScope;\n            if (hasCallsite) {\n                const prevCsi = callsiteSourcesIndex;\n                const prevLine = callsiteLine;\n                callsiteSourcesIndex = decodeInteger(reader, callsiteSourcesIndex);\n                const sameSource = prevCsi === callsiteSourcesIndex;\n                callsiteLine = decodeInteger(reader, sameSource ? callsiteLine : 0);\n                callsiteColumn = decodeInteger(reader, sameSource && prevLine === callsiteLine ? callsiteColumn : 0);\n                callsite = [callsiteSourcesIndex, callsiteLine, callsiteColumn];\n            }\n            range.callsite = callsite;\n            if (hasMoreVlq(reader, semi)) {\n                bindings = [];\n                do {\n                    bindingLine = genLine;\n                    bindingColumn = genColumn;\n                    const expressionsCount = decodeInteger(reader, 0);\n                    let expressionRanges;\n                    if (expressionsCount < -1) {\n                        expressionRanges = [[decodeInteger(reader, 0)]];\n                        for (let i = -1; i > expressionsCount; i--) {\n                            const prevBl = bindingLine;\n                            bindingLine = decodeInteger(reader, bindingLine);\n                            bindingColumn = decodeInteger(reader, bindingLine === prevBl ? bindingColumn : 0);\n                            const expression = decodeInteger(reader, 0);\n                            expressionRanges.push([expression, bindingLine, bindingColumn]);\n                        }\n                    }\n                    else {\n                        expressionRanges = [[expressionsCount]];\n                    }\n                    bindings.push(expressionRanges);\n                } while (hasMoreVlq(reader, semi));\n            }\n            range.bindings = bindings;\n            ranges.push(range);\n            stack.push(range);\n        }\n        genLine++;\n        reader.pos = semi + 1;\n    } while (reader.pos < length);\n    return ranges;\n}\nfunction encodeGeneratedRanges(ranges) {\n    if (ranges.length === 0)\n        return '';\n    const writer = new StringWriter();\n    for (let i = 0; i < ranges.length;) {\n        i = _encodeGeneratedRanges(ranges, i, writer, [0, 0, 0, 0, 0, 0, 0]);\n    }\n    return writer.flush();\n}\nfunction _encodeGeneratedRanges(ranges, index, writer, state) {\n    const range = ranges[index];\n    const { 0: startLine, 1: startColumn, 2: endLine, 3: endColumn, isScope, callsite, bindings, } = range;\n    if (state[0] < startLine) {\n        catchupLine(writer, state[0], startLine);\n        state[0] = startLine;\n        state[1] = 0;\n    }\n    else if (index > 0) {\n        writer.write(comma);\n    }\n    state[1] = encodeInteger(writer, range[1], state[1]);\n    const fields = (range.length === 6 ? 0b0001 : 0) | (callsite ? 0b0010 : 0) | (isScope ? 0b0100 : 0);\n    encodeInteger(writer, fields, 0);\n    if (range.length === 6) {\n        const { 4: sourcesIndex, 5: scopesIndex } = range;\n        if (sourcesIndex !== state[2]) {\n            state[3] = 0;\n        }\n        state[2] = encodeInteger(writer, sourcesIndex, state[2]);\n        state[3] = encodeInteger(writer, scopesIndex, state[3]);\n    }\n    if (callsite) {\n        const { 0: sourcesIndex, 1: callLine, 2: callColumn } = range.callsite;\n        if (sourcesIndex !== state[4]) {\n            state[5] = 0;\n            state[6] = 0;\n        }\n        else if (callLine !== state[5]) {\n            state[6] = 0;\n        }\n        state[4] = encodeInteger(writer, sourcesIndex, state[4]);\n        state[5] = encodeInteger(writer, callLine, state[5]);\n        state[6] = encodeInteger(writer, callColumn, state[6]);\n    }\n    if (bindings) {\n        for (const binding of bindings) {\n            if (binding.length > 1)\n                encodeInteger(writer, -binding.length, 0);\n            const expression = binding[0][0];\n            encodeInteger(writer, expression, 0);\n            let bindingStartLine = startLine;\n            let bindingStartColumn = startColumn;\n            for (let i = 1; i < binding.length; i++) {\n                const expRange = binding[i];\n                bindingStartLine = encodeInteger(writer, expRange[1], bindingStartLine);\n                bindingStartColumn = encodeInteger(writer, expRange[2], bindingStartColumn);\n                encodeInteger(writer, expRange[0], 0);\n            }\n        }\n    }\n    for (index++; index < ranges.length;) {\n        const next = ranges[index];\n        const { 0: l, 1: c } = next;\n        if (l > endLine || (l === endLine && c >= endColumn)) {\n            break;\n        }\n        index = _encodeGeneratedRanges(ranges, index, writer, state);\n    }\n    if (state[0] < endLine) {\n        catchupLine(writer, state[0], endLine);\n        state[0] = endLine;\n        state[1] = 0;\n    }\n    else {\n        writer.write(comma);\n    }\n    state[1] = encodeInteger(writer, endColumn, state[1]);\n    return index;\n}\nfunction catchupLine(writer, lastLine, line) {\n    do {\n        writer.write(semicolon);\n    } while (++lastLine < line);\n}\n\nfunction decode(mappings) {\n    const { length } = mappings;\n    const reader = new StringReader(mappings);\n    const decoded = [];\n    let genColumn = 0;\n    let sourcesIndex = 0;\n    let sourceLine = 0;\n    let sourceColumn = 0;\n    let namesIndex = 0;\n    do {\n        const semi = reader.indexOf(';');\n        const line = [];\n        let sorted = true;\n        let lastCol = 0;\n        genColumn = 0;\n        while (reader.pos < semi) {\n            let seg;\n            genColumn = decodeInteger(reader, genColumn);\n            if (genColumn < lastCol)\n                sorted = false;\n            lastCol = genColumn;\n            if (hasMoreVlq(reader, semi)) {\n                sourcesIndex = decodeInteger(reader, sourcesIndex);\n                sourceLine = decodeInteger(reader, sourceLine);\n                sourceColumn = decodeInteger(reader, sourceColumn);\n                if (hasMoreVlq(reader, semi)) {\n                    namesIndex = decodeInteger(reader, namesIndex);\n                    seg = [genColumn, sourcesIndex, sourceLine, sourceColumn, namesIndex];\n                }\n                else {\n                    seg = [genColumn, sourcesIndex, sourceLine, sourceColumn];\n                }\n            }\n            else {\n                seg = [genColumn];\n            }\n            line.push(seg);\n            reader.pos++;\n        }\n        if (!sorted)\n            sort(line);\n        decoded.push(line);\n        reader.pos = semi + 1;\n    } while (reader.pos <= length);\n    return decoded;\n}\nfunction sort(line) {\n    line.sort(sortComparator);\n}\nfunction sortComparator(a, b) {\n    return a[0] - b[0];\n}\nfunction encode(decoded) {\n    const writer = new StringWriter();\n    let sourcesIndex = 0;\n    let sourceLine = 0;\n    let sourceColumn = 0;\n    let namesIndex = 0;\n    for (let i = 0; i < decoded.length; i++) {\n        const line = decoded[i];\n        if (i > 0)\n            writer.write(semicolon);\n        if (line.length === 0)\n            continue;\n        let genColumn = 0;\n        for (let j = 0; j < line.length; j++) {\n            const segment = line[j];\n            if (j > 0)\n                writer.write(comma);\n            genColumn = encodeInteger(writer, segment[0], genColumn);\n            if (segment.length === 1)\n                continue;\n            sourcesIndex = encodeInteger(writer, segment[1], sourcesIndex);\n            sourceLine = encodeInteger(writer, segment[2], sourceLine);\n            sourceColumn = encodeInteger(writer, segment[3], sourceColumn);\n            if (segment.length === 4)\n                continue;\n            namesIndex = encodeInteger(writer, segment[4], namesIndex);\n        }\n    }\n    return writer.flush();\n}\n\nexport { decode, decodeGeneratedRanges, decodeOriginalScopes, encode, encodeGeneratedRanges, encodeOriginalScopes };\n//# sourceMappingURL=sourcemap-codec.mjs.map\n", "import { encode } from '@jridgewell/sourcemap-codec';\n\nfunction getBtoa() {\n\tif (typeof globalThis !== 'undefined' && typeof globalThis.btoa === 'function') {\n\t\treturn (str) => globalThis.btoa(unescape(encodeURIComponent(str)));\n\t} else if (typeof Buffer === 'function') {\n\t\treturn (str) => Buffer.from(str, 'utf-8').toString('base64');\n\t} else {\n\t\treturn () => {\n\t\t\tthrow new Error('Unsupported environment: `window.btoa` or `Buffer` should be supported.');\n\t\t};\n\t}\n}\n\nconst btoa = /*#__PURE__*/ getBtoa();\n\nexport default class SourceMap {\n\tconstructor(properties) {\n\t\tthis.version = 3;\n\t\tthis.file = properties.file;\n\t\tthis.sources = properties.sources;\n\t\tthis.sourcesContent = properties.sourcesContent;\n\t\tthis.names = properties.names;\n\t\tthis.mappings = encode(properties.mappings);\n\t\tif (typeof properties.x_google_ignoreList !== 'undefined') {\n\t\t\tthis.x_google_ignoreList = properties.x_google_ignoreList;\n\t\t}\n\t\tif (typeof properties.debugId !== 'undefined') {\n\t\t\tthis.debugId = properties.debugId;\n\t\t}\n\t}\n\n\ttoString() {\n\t\treturn JSON.stringify(this);\n\t}\n\n\ttoUrl() {\n\t\treturn 'data:application/json;charset=utf-8;base64,' + btoa(this.toString());\n\t}\n}\n", "export default function guessIndent(code) {\n\tconst lines = code.split('\\n');\n\n\tconst tabbed = lines.filter((line) => /^\\t+/.test(line));\n\tconst spaced = lines.filter((line) => /^ {2,}/.test(line));\n\n\tif (tabbed.length === 0 && spaced.length === 0) {\n\t\treturn null;\n\t}\n\n\t// More lines tabbed than spaced? Assume tabs, and\n\t// default to tabs in the case of a tie (or nothing\n\t// to go on)\n\tif (tabbed.length >= spaced.length) {\n\t\treturn '\\t';\n\t}\n\n\t// Otherwise, we need to guess the multiple\n\tconst min = spaced.reduce((previous, current) => {\n\t\tconst numSpaces = /^ +/.exec(current)[0].length;\n\t\treturn Math.min(numSpaces, previous);\n\t}, Infinity);\n\n\treturn new Array(min + 1).join(' ');\n}\n", "export default function getRelativePath(from, to) {\n\tconst fromParts = from.split(/[/\\\\]/);\n\tconst toParts = to.split(/[/\\\\]/);\n\n\tfromParts.pop(); // get dirname\n\n\twhile (fromParts[0] === toParts[0]) {\n\t\tfromParts.shift();\n\t\ttoParts.shift();\n\t}\n\n\tif (fromParts.length) {\n\t\tlet i = fromParts.length;\n\t\twhile (i--) fromParts[i] = '..';\n\t}\n\n\treturn fromParts.concat(toParts).join('/');\n}\n", "const toString = Object.prototype.toString;\n\nexport default function isObject(thing) {\n\treturn toString.call(thing) === '[object Object]';\n}\n", "export default function getLocator(source) {\n\tconst originalLines = source.split('\\n');\n\tconst lineOffsets = [];\n\n\tfor (let i = 0, pos = 0; i < originalLines.length; i++) {\n\t\tlineOffsets.push(pos);\n\t\tpos += originalLines[i].length + 1;\n\t}\n\n\treturn function locate(index) {\n\t\tlet i = 0;\n\t\tlet j = lineOffsets.length;\n\t\twhile (i < j) {\n\t\t\tconst m = (i + j) >> 1;\n\t\t\tif (index < lineOffsets[m]) {\n\t\t\t\tj = m;\n\t\t\t} else {\n\t\t\t\ti = m + 1;\n\t\t\t}\n\t\t}\n\t\tconst line = i - 1;\n\t\tconst column = index - lineOffsets[line];\n\t\treturn { line, column };\n\t};\n}\n", "const wordRegex = /\\w/;\n\nexport default class Mappings {\n\tconstructor(hires) {\n\t\tthis.hires = hires;\n\t\tthis.generatedCodeLine = 0;\n\t\tthis.generatedCodeColumn = 0;\n\t\tthis.raw = [];\n\t\tthis.rawSegments = this.raw[this.generatedCodeLine] = [];\n\t\tthis.pending = null;\n\t}\n\n\taddEdit(sourceIndex, content, loc, nameIndex) {\n\t\tif (content.length) {\n\t\t\tconst contentLengthMinusOne = content.length - 1;\n\t\t\tlet contentLineEnd = content.indexOf('\\n', 0);\n\t\t\tlet previousContentLineEnd = -1;\n\t\t\t// Loop through each line in the content and add a segment, but stop if the last line is empty,\n\t\t\t// else code afterwards would fill one line too many\n\t\t\twhile (contentLineEnd >= 0 && contentLengthMinusOne > contentLineEnd) {\n\t\t\t\tconst segment = [this.generatedCodeColumn, sourceIndex, loc.line, loc.column];\n\t\t\t\tif (nameIndex >= 0) {\n\t\t\t\t\tsegment.push(nameIndex);\n\t\t\t\t}\n\t\t\t\tthis.rawSegments.push(segment);\n\n\t\t\t\tthis.generatedCodeLine += 1;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t\tthis.generatedCodeColumn = 0;\n\n\t\t\t\tpreviousContentLineEnd = contentLineEnd;\n\t\t\t\tcontentLineEnd = content.indexOf('\\n', contentLineEnd + 1);\n\t\t\t}\n\n\t\t\tconst segment = [this.generatedCodeColumn, sourceIndex, loc.line, loc.column];\n\t\t\tif (nameIndex >= 0) {\n\t\t\t\tsegment.push(nameIndex);\n\t\t\t}\n\t\t\tthis.rawSegments.push(segment);\n\n\t\t\tthis.advance(content.slice(previousContentLineEnd + 1));\n\t\t} else if (this.pending) {\n\t\t\tthis.rawSegments.push(this.pending);\n\t\t\tthis.advance(content);\n\t\t}\n\n\t\tthis.pending = null;\n\t}\n\n\taddUneditedChunk(sourceIndex, chunk, original, loc, sourcemapLocations) {\n\t\tlet originalCharIndex = chunk.start;\n\t\tlet first = true;\n\t\t// when iterating each char, check if it's in a word boundary\n\t\tlet charInHiresBoundary = false;\n\n\t\twhile (originalCharIndex < chunk.end) {\n\t\t\tif (original[originalCharIndex] === '\\n') {\n\t\t\t\tloc.line += 1;\n\t\t\t\tloc.column = 0;\n\t\t\t\tthis.generatedCodeLine += 1;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t\tthis.generatedCodeColumn = 0;\n\t\t\t\tfirst = true;\n\t\t\t\tcharInHiresBoundary = false;\n\t\t\t} else {\n\t\t\t\tif (this.hires || first || sourcemapLocations.has(originalCharIndex)) {\n\t\t\t\t\tconst segment = [this.generatedCodeColumn, sourceIndex, loc.line, loc.column];\n\n\t\t\t\t\tif (this.hires === 'boundary') {\n\t\t\t\t\t\t// in hires \"boundary\", group segments per word boundary than per char\n\t\t\t\t\t\tif (wordRegex.test(original[originalCharIndex])) {\n\t\t\t\t\t\t\t// for first char in the boundary found, start the boundary by pushing a segment\n\t\t\t\t\t\t\tif (!charInHiresBoundary) {\n\t\t\t\t\t\t\t\tthis.rawSegments.push(segment);\n\t\t\t\t\t\t\t\tcharInHiresBoundary = true;\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// for non-word char, end the boundary by pushing a segment\n\t\t\t\t\t\t\tthis.rawSegments.push(segment);\n\t\t\t\t\t\t\tcharInHiresBoundary = false;\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.rawSegments.push(segment);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tloc.column += 1;\n\t\t\t\tthis.generatedCodeColumn += 1;\n\t\t\t\tfirst = false;\n\t\t\t}\n\n\t\t\toriginalCharIndex += 1;\n\t\t}\n\n\t\tthis.pending = null;\n\t}\n\n\tadvance(str) {\n\t\tif (!str) return;\n\n\t\tconst lines = str.split('\\n');\n\n\t\tif (lines.length > 1) {\n\t\t\tfor (let i = 0; i < lines.length - 1; i++) {\n\t\t\t\tthis.generatedCodeLine++;\n\t\t\t\tthis.raw[this.generatedCodeLine] = this.rawSegments = [];\n\t\t\t}\n\t\t\tthis.generatedCodeColumn = 0;\n\t\t}\n\n\t\tthis.generatedCodeColumn += lines[lines.length - 1].length;\n\t}\n}\n", "import BitSet from './BitSet.js';\nimport Chunk from './Chunk.js';\nimport SourceMap from './SourceMap.js';\nimport guessIndent from './utils/guessIndent.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\nimport Stats from './utils/Stats.js';\n\nconst n = '\\n';\n\nconst warned = {\n\tinsertLeft: false,\n\tinsertRight: false,\n\tstoreName: false,\n};\n\nexport default class MagicString {\n\tconstructor(string, options = {}) {\n\t\tconst chunk = new Chunk(0, string.length, string);\n\n\t\tObject.defineProperties(this, {\n\t\t\toriginal: { writable: true, value: string },\n\t\t\toutro: { writable: true, value: '' },\n\t\t\tintro: { writable: true, value: '' },\n\t\t\tfirstChunk: { writable: true, value: chunk },\n\t\t\tlastChunk: { writable: true, value: chunk },\n\t\t\tlastSearchedChunk: { writable: true, value: chunk },\n\t\t\tbyStart: { writable: true, value: {} },\n\t\t\tbyEnd: { writable: true, value: {} },\n\t\t\tfilename: { writable: true, value: options.filename },\n\t\t\tindentExclusionRanges: { writable: true, value: options.indentExclusionRanges },\n\t\t\tsourcemapLocations: { writable: true, value: new BitSet() },\n\t\t\tstoredNames: { writable: true, value: {} },\n\t\t\tindentStr: { writable: true, value: undefined },\n\t\t\tignoreList: { writable: true, value: options.ignoreList },\n\t\t\toffset: { writable: true, value: options.offset || 0 },\n\t\t});\n\n\t\tif (DEBUG) {\n\t\t\tObject.defineProperty(this, 'stats', { value: new Stats() });\n\t\t}\n\n\t\tthis.byStart[0] = chunk;\n\t\tthis.byEnd[string.length] = chunk;\n\t}\n\n\taddSourcemapLocation(char) {\n\t\tthis.sourcemapLocations.add(char);\n\t}\n\n\tappend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.outro += content;\n\t\treturn this;\n\t}\n\n\tappendLeft(index, content) {\n\t\tindex = index + this.offset;\n\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendLeft');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendLeft(content);\n\t\t} else {\n\t\t\tthis.intro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendLeft');\n\t\treturn this;\n\t}\n\n\tappendRight(index, content) {\n\t\tindex = index + this.offset;\n\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('appendRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.appendRight(content);\n\t\t} else {\n\t\t\tthis.outro += content;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('appendRight');\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst cloned = new MagicString(this.original, { filename: this.filename, offset: this.offset });\n\n\t\tlet originalChunk = this.firstChunk;\n\t\tlet clonedChunk = (cloned.firstChunk = cloned.lastSearchedChunk = originalChunk.clone());\n\n\t\twhile (originalChunk) {\n\t\t\tcloned.byStart[clonedChunk.start] = clonedChunk;\n\t\t\tcloned.byEnd[clonedChunk.end] = clonedChunk;\n\n\t\t\tconst nextOriginalChunk = originalChunk.next;\n\t\t\tconst nextClonedChunk = nextOriginalChunk && nextOriginalChunk.clone();\n\n\t\t\tif (nextClonedChunk) {\n\t\t\t\tclonedChunk.next = nextClonedChunk;\n\t\t\t\tnextClonedChunk.previous = clonedChunk;\n\n\t\t\t\tclonedChunk = nextClonedChunk;\n\t\t\t}\n\n\t\t\toriginalChunk = nextOriginalChunk;\n\t\t}\n\n\t\tcloned.lastChunk = clonedChunk;\n\n\t\tif (this.indentExclusionRanges) {\n\t\t\tcloned.indentExclusionRanges = this.indentExclusionRanges.slice();\n\t\t}\n\n\t\tcloned.sourcemapLocations = new BitSet(this.sourcemapLocations);\n\n\t\tcloned.intro = this.intro;\n\t\tcloned.outro = this.outro;\n\n\t\treturn cloned;\n\t}\n\n\tgenerateDecodedMap(options) {\n\t\toptions = options || {};\n\n\t\tconst sourceIndex = 0;\n\t\tconst names = Object.keys(this.storedNames);\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tconst locate = getLocator(this.original);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.firstChunk.eachNext((chunk) => {\n\t\t\tconst loc = locate(chunk.start);\n\n\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tmappings.addEdit(\n\t\t\t\t\tsourceIndex,\n\t\t\t\t\tchunk.content,\n\t\t\t\t\tloc,\n\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1,\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tmappings.addUneditedChunk(sourceIndex, chunk, this.original, loc, this.sourcemapLocations);\n\t\t\t}\n\n\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : undefined,\n\t\t\tsources: [\n\t\t\t\toptions.source ? getRelativePath(options.file || '', options.source) : options.file || '',\n\t\t\t],\n\t\t\tsourcesContent: options.includeContent ? [this.original] : undefined,\n\t\t\tnames,\n\t\t\tmappings: mappings.raw,\n\t\t\tx_google_ignoreList: this.ignoreList ? [sourceIndex] : undefined,\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\t_ensureindentStr() {\n\t\tif (this.indentStr === undefined) {\n\t\t\tthis.indentStr = guessIndent(this.original);\n\t\t}\n\t}\n\n\t_getRawIndentString() {\n\t\tthis._ensureindentStr();\n\t\treturn this.indentStr;\n\t}\n\n\tgetIndentString() {\n\t\tthis._ensureindentStr();\n\t\treturn this.indentStr === null ? '\\t' : this.indentStr;\n\t}\n\n\tindent(indentStr, options) {\n\t\tconst pattern = /^[^\\r\\n]/gm;\n\n\t\tif (isObject(indentStr)) {\n\t\t\toptions = indentStr;\n\t\t\tindentStr = undefined;\n\t\t}\n\n\t\tif (indentStr === undefined) {\n\t\t\tthis._ensureindentStr();\n\t\t\tindentStr = this.indentStr || '\\t';\n\t\t}\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\toptions = options || {};\n\n\t\t// Process exclusion ranges\n\t\tconst isExcluded = {};\n\n\t\tif (options.exclude) {\n\t\t\tconst exclusions =\n\t\t\t\ttypeof options.exclude[0] === 'number' ? [options.exclude] : options.exclude;\n\t\t\texclusions.forEach((exclusion) => {\n\t\t\t\tfor (let i = exclusion[0]; i < exclusion[1]; i += 1) {\n\t\t\t\t\tisExcluded[i] = true;\n\t\t\t\t}\n\t\t\t});\n\t\t}\n\n\t\tlet shouldIndentNextCharacter = options.indentStart !== false;\n\t\tconst replacer = (match) => {\n\t\t\tif (shouldIndentNextCharacter) return `${indentStr}${match}`;\n\t\t\tshouldIndentNextCharacter = true;\n\t\t\treturn match;\n\t\t};\n\n\t\tthis.intro = this.intro.replace(pattern, replacer);\n\n\t\tlet charIndex = 0;\n\t\tlet chunk = this.firstChunk;\n\n\t\twhile (chunk) {\n\t\t\tconst end = chunk.end;\n\n\t\t\tif (chunk.edited) {\n\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\tchunk.content = chunk.content.replace(pattern, replacer);\n\n\t\t\t\t\tif (chunk.content.length) {\n\t\t\t\t\t\tshouldIndentNextCharacter = chunk.content[chunk.content.length - 1] === '\\n';\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tcharIndex = chunk.start;\n\n\t\t\t\twhile (charIndex < end) {\n\t\t\t\t\tif (!isExcluded[charIndex]) {\n\t\t\t\t\t\tconst char = this.original[charIndex];\n\n\t\t\t\t\t\tif (char === '\\n') {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = true;\n\t\t\t\t\t\t} else if (char !== '\\r' && shouldIndentNextCharacter) {\n\t\t\t\t\t\t\tshouldIndentNextCharacter = false;\n\n\t\t\t\t\t\t\tif (charIndex === chunk.start) {\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tthis._splitChunk(chunk, charIndex);\n\t\t\t\t\t\t\t\tchunk = chunk.next;\n\t\t\t\t\t\t\t\tchunk.prependRight(indentStr);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\tcharIndex += 1;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tcharIndex = chunk.end;\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tthis.outro = this.outro.replace(pattern, replacer);\n\n\t\treturn this;\n\t}\n\n\tinsert() {\n\t\tthrow new Error(\n\t\t\t'magicString.insert(...) is deprecated. Use prependRight(...) or appendLeft(...)',\n\t\t);\n\t}\n\n\tinsertLeft(index, content) {\n\t\tif (!warned.insertLeft) {\n\t\t\tconsole.warn(\n\t\t\t\t'magicString.insertLeft(...) is deprecated. Use magicString.appendLeft(...) instead',\n\t\t\t);\n\t\t\twarned.insertLeft = true;\n\t\t}\n\n\t\treturn this.appendLeft(index, content);\n\t}\n\n\tinsertRight(index, content) {\n\t\tif (!warned.insertRight) {\n\t\t\tconsole.warn(\n\t\t\t\t'magicString.insertRight(...) is deprecated. Use magicString.prependRight(...) instead',\n\t\t\t);\n\t\t\twarned.insertRight = true;\n\t\t}\n\n\t\treturn this.prependRight(index, content);\n\t}\n\n\tmove(start, end, index) {\n\t\tstart = start + this.offset;\n\t\tend = end + this.offset;\n\t\tindex = index + this.offset;\n\n\t\tif (index >= start && index <= end) throw new Error('Cannot move a selection inside itself');\n\n\t\tif (DEBUG) this.stats.time('move');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\t\tthis._split(index);\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tconst oldLeft = first.previous;\n\t\tconst oldRight = last.next;\n\n\t\tconst newRight = this.byStart[index];\n\t\tif (!newRight && last === this.lastChunk) return this;\n\t\tconst newLeft = newRight ? newRight.previous : this.lastChunk;\n\n\t\tif (oldLeft) oldLeft.next = oldRight;\n\t\tif (oldRight) oldRight.previous = oldLeft;\n\n\t\tif (newLeft) newLeft.next = first;\n\t\tif (newRight) newRight.previous = last;\n\n\t\tif (!first.previous) this.firstChunk = last.next;\n\t\tif (!last.next) {\n\t\t\tthis.lastChunk = first.previous;\n\t\t\tthis.lastChunk.next = null;\n\t\t}\n\n\t\tfirst.previous = newLeft;\n\t\tlast.next = newRight || null;\n\n\t\tif (!newLeft) this.firstChunk = first;\n\t\tif (!newRight) this.lastChunk = last;\n\n\t\tif (DEBUG) this.stats.timeEnd('move');\n\t\treturn this;\n\t}\n\n\toverwrite(start, end, content, options) {\n\t\toptions = options || {};\n\t\treturn this.update(start, end, content, { ...options, overwrite: !options.contentOnly });\n\t}\n\n\tupdate(start, end, content, options) {\n\t\tstart = start + this.offset;\n\t\tend = end + this.offset;\n\n\t\tif (typeof content !== 'string') throw new TypeError('replacement content must be a string');\n\n\t\tif (this.original.length !== 0) {\n\t\t\twhile (start < 0) start += this.original.length;\n\t\t\twhile (end < 0) end += this.original.length;\n\t\t}\n\n\t\tif (end > this.original.length) throw new Error('end is out of bounds');\n\t\tif (start === end)\n\t\t\tthrow new Error(\n\t\t\t\t'Cannot overwrite a zero-length range – use appendLeft or prependRight instead',\n\t\t\t);\n\n\t\tif (DEBUG) this.stats.time('overwrite');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tif (options === true) {\n\t\t\tif (!warned.storeName) {\n\t\t\t\tconsole.warn(\n\t\t\t\t\t'The final argument to magicString.overwrite(...) should be an options object. See https://github.com/rich-harris/magic-string',\n\t\t\t\t);\n\t\t\t\twarned.storeName = true;\n\t\t\t}\n\n\t\t\toptions = { storeName: true };\n\t\t}\n\t\tconst storeName = options !== undefined ? options.storeName : false;\n\t\tconst overwrite = options !== undefined ? options.overwrite : false;\n\n\t\tif (storeName) {\n\t\t\tconst original = this.original.slice(start, end);\n\t\t\tObject.defineProperty(this.storedNames, original, {\n\t\t\t\twritable: true,\n\t\t\t\tvalue: true,\n\t\t\t\tenumerable: true,\n\t\t\t});\n\t\t}\n\n\t\tconst first = this.byStart[start];\n\t\tconst last = this.byEnd[end];\n\n\t\tif (first) {\n\t\t\tlet chunk = first;\n\t\t\twhile (chunk !== last) {\n\t\t\t\tif (chunk.next !== this.byStart[chunk.end]) {\n\t\t\t\t\tthrow new Error('Cannot overwrite across a split point');\n\t\t\t\t}\n\t\t\t\tchunk = chunk.next;\n\t\t\t\tchunk.edit('', false);\n\t\t\t}\n\n\t\t\tfirst.edit(content, storeName, !overwrite);\n\t\t} else {\n\t\t\t// must be inserting at the end\n\t\t\tconst newChunk = new Chunk(start, end, '').edit(content, storeName);\n\n\t\t\t// TODO last chunk in the array may not be the last chunk, if it's moved...\n\t\t\tlast.next = newChunk;\n\t\t\tnewChunk.previous = last;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('overwrite');\n\t\treturn this;\n\t}\n\n\tprepend(content) {\n\t\tif (typeof content !== 'string') throw new TypeError('outro content must be a string');\n\n\t\tthis.intro = content + this.intro;\n\t\treturn this;\n\t}\n\n\tprependLeft(index, content) {\n\t\tindex = index + this.offset;\n\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byEnd[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependLeft(content);\n\t\t} else {\n\t\t\tthis.intro = content + this.intro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tprependRight(index, content) {\n\t\tindex = index + this.offset;\n\n\t\tif (typeof content !== 'string') throw new TypeError('inserted content must be a string');\n\n\t\tif (DEBUG) this.stats.time('insertRight');\n\n\t\tthis._split(index);\n\n\t\tconst chunk = this.byStart[index];\n\n\t\tif (chunk) {\n\t\t\tchunk.prependRight(content);\n\t\t} else {\n\t\t\tthis.outro = content + this.outro;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('insertRight');\n\t\treturn this;\n\t}\n\n\tremove(start, end) {\n\t\tstart = start + this.offset;\n\t\tend = end + this.offset;\n\n\t\tif (this.original.length !== 0) {\n\t\t\twhile (start < 0) start += this.original.length;\n\t\t\twhile (end < 0) end += this.original.length;\n\t\t}\n\n\t\tif (start === end) return this;\n\n\t\tif (start < 0 || end > this.original.length) throw new Error('Character is out of bounds');\n\t\tif (start > end) throw new Error('end must be greater than start');\n\n\t\tif (DEBUG) this.stats.time('remove');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tlet chunk = this.byStart[start];\n\n\t\twhile (chunk) {\n\t\t\tchunk.intro = '';\n\t\t\tchunk.outro = '';\n\t\t\tchunk.edit('');\n\n\t\t\tchunk = end > chunk.end ? this.byStart[chunk.end] : null;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('remove');\n\t\treturn this;\n\t}\n\n\treset(start, end) {\n\t\tstart = start + this.offset;\n\t\tend = end + this.offset;\n\n\t\tif (this.original.length !== 0) {\n\t\t\twhile (start < 0) start += this.original.length;\n\t\t\twhile (end < 0) end += this.original.length;\n\t\t}\n\n\t\tif (start === end) return this;\n\n\t\tif (start < 0 || end > this.original.length) throw new Error('Character is out of bounds');\n\t\tif (start > end) throw new Error('end must be greater than start');\n\n\t\tif (DEBUG) this.stats.time('reset');\n\n\t\tthis._split(start);\n\t\tthis._split(end);\n\n\t\tlet chunk = this.byStart[start];\n\n\t\twhile (chunk) {\n\t\t\tchunk.reset();\n\n\t\t\tchunk = end > chunk.end ? this.byStart[chunk.end] : null;\n\t\t}\n\n\t\tif (DEBUG) this.stats.timeEnd('reset');\n\t\treturn this;\n\t}\n\n\tlastChar() {\n\t\tif (this.outro.length) return this.outro[this.outro.length - 1];\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length) return chunk.outro[chunk.outro.length - 1];\n\t\t\tif (chunk.content.length) return chunk.content[chunk.content.length - 1];\n\t\t\tif (chunk.intro.length) return chunk.intro[chunk.intro.length - 1];\n\t\t} while ((chunk = chunk.previous));\n\t\tif (this.intro.length) return this.intro[this.intro.length - 1];\n\t\treturn '';\n\t}\n\n\tlastLine() {\n\t\tlet lineIndex = this.outro.lastIndexOf(n);\n\t\tif (lineIndex !== -1) return this.outro.substr(lineIndex + 1);\n\t\tlet lineStr = this.outro;\n\t\tlet chunk = this.lastChunk;\n\t\tdo {\n\t\t\tif (chunk.outro.length > 0) {\n\t\t\t\tlineIndex = chunk.outro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.outro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.outro + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.content.length > 0) {\n\t\t\t\tlineIndex = chunk.content.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.content.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.content + lineStr;\n\t\t\t}\n\n\t\t\tif (chunk.intro.length > 0) {\n\t\t\t\tlineIndex = chunk.intro.lastIndexOf(n);\n\t\t\t\tif (lineIndex !== -1) return chunk.intro.substr(lineIndex + 1) + lineStr;\n\t\t\t\tlineStr = chunk.intro + lineStr;\n\t\t\t}\n\t\t} while ((chunk = chunk.previous));\n\t\tlineIndex = this.intro.lastIndexOf(n);\n\t\tif (lineIndex !== -1) return this.intro.substr(lineIndex + 1) + lineStr;\n\t\treturn this.intro + lineStr;\n\t}\n\n\tslice(start = 0, end = this.original.length - this.offset) {\n\t\tstart = start + this.offset;\n\t\tend = end + this.offset;\n\n\t\tif (this.original.length !== 0) {\n\t\t\twhile (start < 0) start += this.original.length;\n\t\t\twhile (end < 0) end += this.original.length;\n\t\t}\n\n\t\tlet result = '';\n\n\t\t// find start chunk\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk && (chunk.start > start || chunk.end <= start)) {\n\t\t\t// found end chunk before start\n\t\t\tif (chunk.start < end && chunk.end >= end) {\n\t\t\t\treturn result;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\tif (chunk && chunk.edited && chunk.start !== start)\n\t\t\tthrow new Error(`Cannot use replaced character ${start} as slice start anchor.`);\n\n\t\tconst startChunk = chunk;\n\t\twhile (chunk) {\n\t\t\tif (chunk.intro && (startChunk !== chunk || chunk.start === start)) {\n\t\t\t\tresult += chunk.intro;\n\t\t\t}\n\n\t\t\tconst containsEnd = chunk.start < end && chunk.end >= end;\n\t\t\tif (containsEnd && chunk.edited && chunk.end !== end)\n\t\t\t\tthrow new Error(`Cannot use replaced character ${end} as slice end anchor.`);\n\n\t\t\tconst sliceStart = startChunk === chunk ? start - chunk.start : 0;\n\t\t\tconst sliceEnd = containsEnd ? chunk.content.length + end - chunk.end : chunk.content.length;\n\n\t\t\tresult += chunk.content.slice(sliceStart, sliceEnd);\n\n\t\t\tif (chunk.outro && (!containsEnd || chunk.end === end)) {\n\t\t\t\tresult += chunk.outro;\n\t\t\t}\n\n\t\t\tif (containsEnd) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn result;\n\t}\n\n\t// TODO deprecate this? not really very useful\n\tsnip(start, end) {\n\t\tconst clone = this.clone();\n\t\tclone.remove(0, start);\n\t\tclone.remove(end, clone.original.length);\n\n\t\treturn clone;\n\t}\n\n\t_split(index) {\n\t\tif (this.byStart[index] || this.byEnd[index]) return;\n\n\t\tif (DEBUG) this.stats.time('_split');\n\n\t\tlet chunk = this.lastSearchedChunk;\n\t\tconst searchForward = index > chunk.end;\n\n\t\twhile (chunk) {\n\t\t\tif (chunk.contains(index)) return this._splitChunk(chunk, index);\n\n\t\t\tchunk = searchForward ? this.byStart[chunk.end] : this.byEnd[chunk.start];\n\t\t}\n\t}\n\n\t_splitChunk(chunk, index) {\n\t\tif (chunk.edited && chunk.content.length) {\n\t\t\t// zero-length edited chunks are a special case (overlapping replacements)\n\t\t\tconst loc = getLocator(this.original)(index);\n\t\t\tthrow new Error(\n\t\t\t\t`Cannot split a chunk that has already been edited (${loc.line}:${loc.column} – \"${chunk.original}\")`,\n\t\t\t);\n\t\t}\n\n\t\tconst newChunk = chunk.split(index);\n\n\t\tthis.byEnd[index] = chunk;\n\t\tthis.byStart[index] = newChunk;\n\t\tthis.byEnd[newChunk.end] = newChunk;\n\n\t\tif (chunk === this.lastChunk) this.lastChunk = newChunk;\n\n\t\tthis.lastSearchedChunk = chunk;\n\t\tif (DEBUG) this.stats.timeEnd('_split');\n\t\treturn true;\n\t}\n\n\ttoString() {\n\t\tlet str = this.intro;\n\n\t\tlet chunk = this.firstChunk;\n\t\twhile (chunk) {\n\t\t\tstr += chunk.toString();\n\t\t\tchunk = chunk.next;\n\t\t}\n\n\t\treturn str + this.outro;\n\t}\n\n\tisEmpty() {\n\t\tlet chunk = this.firstChunk;\n\t\tdo {\n\t\t\tif (\n\t\t\t\t(chunk.intro.length && chunk.intro.trim()) ||\n\t\t\t\t(chunk.content.length && chunk.content.trim()) ||\n\t\t\t\t(chunk.outro.length && chunk.outro.trim())\n\t\t\t)\n\t\t\t\treturn false;\n\t\t} while ((chunk = chunk.next));\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\tlet chunk = this.firstChunk;\n\t\tlet length = 0;\n\t\tdo {\n\t\t\tlength += chunk.intro.length + chunk.content.length + chunk.outro.length;\n\t\t} while ((chunk = chunk.next));\n\t\treturn length;\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimEndAborted(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tthis.outro = this.outro.replace(rx, '');\n\t\tif (this.outro.length) return true;\n\n\t\tlet chunk = this.lastChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimEnd(rx);\n\n\t\t\t// if chunk was trimmed, we have a new lastChunk\n\t\t\tif (chunk.end !== end) {\n\t\t\t\tif (this.lastChunk === chunk) {\n\t\t\t\t\tthis.lastChunk = chunk.next;\n\t\t\t\t}\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.previous;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimEnd(charType) {\n\t\tthis.trimEndAborted(charType);\n\t\treturn this;\n\t}\n\ttrimStartAborted(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\n\t\tthis.intro = this.intro.replace(rx, '');\n\t\tif (this.intro.length) return true;\n\n\t\tlet chunk = this.firstChunk;\n\n\t\tdo {\n\t\t\tconst end = chunk.end;\n\t\t\tconst aborted = chunk.trimStart(rx);\n\n\t\t\tif (chunk.end !== end) {\n\t\t\t\t// special case...\n\t\t\t\tif (chunk === this.lastChunk) this.lastChunk = chunk.next;\n\n\t\t\t\tthis.byEnd[chunk.end] = chunk;\n\t\t\t\tthis.byStart[chunk.next.start] = chunk.next;\n\t\t\t\tthis.byEnd[chunk.next.end] = chunk.next;\n\t\t\t}\n\n\t\t\tif (aborted) return true;\n\t\t\tchunk = chunk.next;\n\t\t} while (chunk);\n\n\t\treturn false;\n\t}\n\n\ttrimStart(charType) {\n\t\tthis.trimStartAborted(charType);\n\t\treturn this;\n\t}\n\n\thasChanged() {\n\t\treturn this.original !== this.toString();\n\t}\n\n\t_replaceRegexp(searchValue, replacement) {\n\t\tfunction getReplacement(match, str) {\n\t\t\tif (typeof replacement === 'string') {\n\t\t\t\treturn replacement.replace(/\\$(\\$|&|\\d+)/g, (_, i) => {\n\t\t\t\t\t// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/String/replace#specifying_a_string_as_a_parameter\n\t\t\t\t\tif (i === '$') return '$';\n\t\t\t\t\tif (i === '&') return match[0];\n\t\t\t\t\tconst num = +i;\n\t\t\t\t\tif (num < match.length) return match[+i];\n\t\t\t\t\treturn `$${i}`;\n\t\t\t\t});\n\t\t\t} else {\n\t\t\t\treturn replacement(...match, match.index, str, match.groups);\n\t\t\t}\n\t\t}\n\t\tfunction matchAll(re, str) {\n\t\t\tlet match;\n\t\t\tconst matches = [];\n\t\t\twhile ((match = re.exec(str))) {\n\t\t\t\tmatches.push(match);\n\t\t\t}\n\t\t\treturn matches;\n\t\t}\n\t\tif (searchValue.global) {\n\t\t\tconst matches = matchAll(searchValue, this.original);\n\t\t\tmatches.forEach((match) => {\n\t\t\t\tif (match.index != null) {\n\t\t\t\t\tconst replacement = getReplacement(match, this.original);\n\t\t\t\t\tif (replacement !== match[0]) {\n\t\t\t\t\t\tthis.overwrite(match.index, match.index + match[0].length, replacement);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t});\n\t\t} else {\n\t\t\tconst match = this.original.match(searchValue);\n\t\t\tif (match && match.index != null) {\n\t\t\t\tconst replacement = getReplacement(match, this.original);\n\t\t\t\tif (replacement !== match[0]) {\n\t\t\t\t\tthis.overwrite(match.index, match.index + match[0].length, replacement);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn this;\n\t}\n\n\t_replaceString(string, replacement) {\n\t\tconst { original } = this;\n\t\tconst index = original.indexOf(string);\n\n\t\tif (index !== -1) {\n\t\t\tthis.overwrite(index, index + string.length, replacement);\n\t\t}\n\n\t\treturn this;\n\t}\n\n\treplace(searchValue, replacement) {\n\t\tif (typeof searchValue === 'string') {\n\t\t\treturn this._replaceString(searchValue, replacement);\n\t\t}\n\n\t\treturn this._replaceRegexp(searchValue, replacement);\n\t}\n\n\t_replaceAllString(string, replacement) {\n\t\tconst { original } = this;\n\t\tconst stringLength = string.length;\n\t\tfor (\n\t\t\tlet index = original.indexOf(string);\n\t\t\tindex !== -1;\n\t\t\tindex = original.indexOf(string, index + stringLength)\n\t\t) {\n\t\t\tconst previous = original.slice(index, index + stringLength);\n\t\t\tif (previous !== replacement) this.overwrite(index, index + stringLength, replacement);\n\t\t}\n\n\t\treturn this;\n\t}\n\n\treplaceAll(searchValue, replacement) {\n\t\tif (typeof searchValue === 'string') {\n\t\t\treturn this._replaceAllString(searchValue, replacement);\n\t\t}\n\n\t\tif (!searchValue.global) {\n\t\t\tthrow new TypeError(\n\t\t\t\t'MagicString.prototype.replaceAll called with a non-global RegExp argument',\n\t\t\t);\n\t\t}\n\n\t\treturn this._replaceRegexp(searchValue, replacement);\n\t}\n}\n", "import MagicString from './MagicString.js';\nimport SourceMap from './SourceMap.js';\nimport getRelativePath from './utils/getRelativePath.js';\nimport isObject from './utils/isObject.js';\nimport getLocator from './utils/getLocator.js';\nimport Mappings from './utils/Mappings.js';\n\nconst hasOwnProp = Object.prototype.hasOwnProperty;\n\nexport default class Bundle {\n\tconstructor(options = {}) {\n\t\tthis.intro = options.intro || '';\n\t\tthis.separator = options.separator !== undefined ? options.separator : '\\n';\n\t\tthis.sources = [];\n\t\tthis.uniqueSources = [];\n\t\tthis.uniqueSourceIndexByFilename = {};\n\t}\n\n\taddSource(source) {\n\t\tif (source instanceof MagicString) {\n\t\t\treturn this.addSource({\n\t\t\t\tcontent: source,\n\t\t\t\tfilename: source.filename,\n\t\t\t\tseparator: this.separator,\n\t\t\t});\n\t\t}\n\n\t\tif (!isObject(source) || !source.content) {\n\t\t\tthrow new Error(\n\t\t\t\t'bundle.addSource() takes an object with a `content` property, which should be an instance of MagicString, and an optional `filename`',\n\t\t\t);\n\t\t}\n\n\t\t['filename', 'ignoreList', 'indentExclusionRanges', 'separator'].forEach((option) => {\n\t\t\tif (!hasOwnProp.call(source, option)) source[option] = source.content[option];\n\t\t});\n\n\t\tif (source.separator === undefined) {\n\t\t\t// TODO there's a bunch of this sort of thing, needs cleaning up\n\t\t\tsource.separator = this.separator;\n\t\t}\n\n\t\tif (source.filename) {\n\t\t\tif (!hasOwnProp.call(this.uniqueSourceIndexByFilename, source.filename)) {\n\t\t\t\tthis.uniqueSourceIndexByFilename[source.filename] = this.uniqueSources.length;\n\t\t\t\tthis.uniqueSources.push({ filename: source.filename, content: source.content.original });\n\t\t\t} else {\n\t\t\t\tconst uniqueSource = this.uniqueSources[this.uniqueSourceIndexByFilename[source.filename]];\n\t\t\t\tif (source.content.original !== uniqueSource.content) {\n\t\t\t\t\tthrow new Error(`Illegal source: same filename (${source.filename}), different contents`);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.sources.push(source);\n\t\treturn this;\n\t}\n\n\tappend(str, options) {\n\t\tthis.addSource({\n\t\t\tcontent: new MagicString(str),\n\t\t\tseparator: (options && options.separator) || '',\n\t\t});\n\n\t\treturn this;\n\t}\n\n\tclone() {\n\t\tconst bundle = new Bundle({\n\t\t\tintro: this.intro,\n\t\t\tseparator: this.separator,\n\t\t});\n\n\t\tthis.sources.forEach((source) => {\n\t\t\tbundle.addSource({\n\t\t\t\tfilename: source.filename,\n\t\t\t\tcontent: source.content.clone(),\n\t\t\t\tseparator: source.separator,\n\t\t\t});\n\t\t});\n\n\t\treturn bundle;\n\t}\n\n\tgenerateDecodedMap(options = {}) {\n\t\tconst names = [];\n\t\tlet x_google_ignoreList = undefined;\n\t\tthis.sources.forEach((source) => {\n\t\t\tObject.keys(source.content.storedNames).forEach((name) => {\n\t\t\t\tif (!~names.indexOf(name)) names.push(name);\n\t\t\t});\n\t\t});\n\n\t\tconst mappings = new Mappings(options.hires);\n\n\t\tif (this.intro) {\n\t\t\tmappings.advance(this.intro);\n\t\t}\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tif (i > 0) {\n\t\t\t\tmappings.advance(this.separator);\n\t\t\t}\n\n\t\t\tconst sourceIndex = source.filename ? this.uniqueSourceIndexByFilename[source.filename] : -1;\n\t\t\tconst magicString = source.content;\n\t\t\tconst locate = getLocator(magicString.original);\n\n\t\t\tif (magicString.intro) {\n\t\t\t\tmappings.advance(magicString.intro);\n\t\t\t}\n\n\t\t\tmagicString.firstChunk.eachNext((chunk) => {\n\t\t\t\tconst loc = locate(chunk.start);\n\n\t\t\t\tif (chunk.intro.length) mappings.advance(chunk.intro);\n\n\t\t\t\tif (source.filename) {\n\t\t\t\t\tif (chunk.edited) {\n\t\t\t\t\t\tmappings.addEdit(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk.content,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tchunk.storeName ? names.indexOf(chunk.original) : -1,\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tmappings.addUneditedChunk(\n\t\t\t\t\t\t\tsourceIndex,\n\t\t\t\t\t\t\tchunk,\n\t\t\t\t\t\t\tmagicString.original,\n\t\t\t\t\t\t\tloc,\n\t\t\t\t\t\t\tmagicString.sourcemapLocations,\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\tmappings.advance(chunk.content);\n\t\t\t\t}\n\n\t\t\t\tif (chunk.outro.length) mappings.advance(chunk.outro);\n\t\t\t});\n\n\t\t\tif (magicString.outro) {\n\t\t\t\tmappings.advance(magicString.outro);\n\t\t\t}\n\n\t\t\tif (source.ignoreList && sourceIndex !== -1) {\n\t\t\t\tif (x_google_ignoreList === undefined) {\n\t\t\t\t\tx_google_ignoreList = [];\n\t\t\t\t}\n\t\t\t\tx_google_ignoreList.push(sourceIndex);\n\t\t\t}\n\t\t});\n\n\t\treturn {\n\t\t\tfile: options.file ? options.file.split(/[/\\\\]/).pop() : undefined,\n\t\t\tsources: this.uniqueSources.map((source) => {\n\t\t\t\treturn options.file ? getRelativePath(options.file, source.filename) : source.filename;\n\t\t\t}),\n\t\t\tsourcesContent: this.uniqueSources.map((source) => {\n\t\t\t\treturn options.includeContent ? source.content : null;\n\t\t\t}),\n\t\t\tnames,\n\t\t\tmappings: mappings.raw,\n\t\t\tx_google_ignoreList,\n\t\t};\n\t}\n\n\tgenerateMap(options) {\n\t\treturn new SourceMap(this.generateDecodedMap(options));\n\t}\n\n\tgetIndentString() {\n\t\tconst indentStringCounts = {};\n\n\t\tthis.sources.forEach((source) => {\n\t\t\tconst indentStr = source.content._getRawIndentString();\n\n\t\t\tif (indentStr === null) return;\n\n\t\t\tif (!indentStringCounts[indentStr]) indentStringCounts[indentStr] = 0;\n\t\t\tindentStringCounts[indentStr] += 1;\n\t\t});\n\n\t\treturn (\n\t\t\tObject.keys(indentStringCounts).sort((a, b) => {\n\t\t\t\treturn indentStringCounts[a] - indentStringCounts[b];\n\t\t\t})[0] || '\\t'\n\t\t);\n\t}\n\n\tindent(indentStr) {\n\t\tif (!arguments.length) {\n\t\t\tindentStr = this.getIndentString();\n\t\t}\n\n\t\tif (indentStr === '') return this; // noop\n\n\t\tlet trailingNewline = !this.intro || this.intro.slice(-1) === '\\n';\n\n\t\tthis.sources.forEach((source, i) => {\n\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\tconst indentStart = trailingNewline || (i > 0 && /\\r?\\n$/.test(separator));\n\n\t\t\tsource.content.indent(indentStr, {\n\t\t\t\texclude: source.indentExclusionRanges,\n\t\t\t\tindentStart, //: trailingNewline || /\\r?\\n$/.test( separator )  //true///\\r?\\n/.test( separator )\n\t\t\t});\n\n\t\t\ttrailingNewline = source.content.lastChar() === '\\n';\n\t\t});\n\n\t\tif (this.intro) {\n\t\t\tthis.intro =\n\t\t\t\tindentStr +\n\t\t\t\tthis.intro.replace(/^[^\\n]/gm, (match, index) => {\n\t\t\t\t\treturn index > 0 ? indentStr + match : match;\n\t\t\t\t});\n\t\t}\n\n\t\treturn this;\n\t}\n\n\tprepend(str) {\n\t\tthis.intro = str + this.intro;\n\t\treturn this;\n\t}\n\n\ttoString() {\n\t\tconst body = this.sources\n\t\t\t.map((source, i) => {\n\t\t\t\tconst separator = source.separator !== undefined ? source.separator : this.separator;\n\t\t\t\tconst str = (i > 0 ? separator : '') + source.content.toString();\n\n\t\t\t\treturn str;\n\t\t\t})\n\t\t\t.join('');\n\n\t\treturn this.intro + body;\n\t}\n\n\tisEmpty() {\n\t\tif (this.intro.length && this.intro.trim()) return false;\n\t\tif (this.sources.some((source) => !source.content.isEmpty())) return false;\n\t\treturn true;\n\t}\n\n\tlength() {\n\t\treturn this.sources.reduce(\n\t\t\t(length, source) => length + source.content.length(),\n\t\t\tthis.intro.length,\n\t\t);\n\t}\n\n\ttrimLines() {\n\t\treturn this.trim('[\\\\r\\\\n]');\n\t}\n\n\ttrim(charType) {\n\t\treturn this.trimStart(charType).trimEnd(charType);\n\t}\n\n\ttrimStart(charType) {\n\t\tconst rx = new RegExp('^' + (charType || '\\\\s') + '+');\n\t\tthis.intro = this.intro.replace(rx, '');\n\n\t\tif (!this.intro) {\n\t\t\tlet source;\n\t\t\tlet i = 0;\n\n\t\t\tdo {\n\t\t\t\tsource = this.sources[i++];\n\t\t\t\tif (!source) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t} while (!source.content.trimStartAborted(charType));\n\t\t}\n\n\t\treturn this;\n\t}\n\n\ttrimEnd(charType) {\n\t\tconst rx = new RegExp((charType || '\\\\s') + '+$');\n\n\t\tlet source;\n\t\tlet i = this.sources.length - 1;\n\n\t\tdo {\n\t\t\tsource = this.sources[i--];\n\t\t\tif (!source) {\n\t\t\t\tthis.intro = this.intro.replace(rx, '');\n\t\t\t\tbreak;\n\t\t\t}\n\t\t} while (!source.content.trimEndAborted(charType));\n\n\t\treturn this;\n\t}\n}\n", "import MagicString from './MagicString.js';\nimport Bundle from './Bundle.js';\nimport SourceMap from './SourceMap.js';\n\nMagicString.Bundle = Bundle;\nMagicString.SourceMap = SourceMap;\nMagicString.default = MagicString; // work around TypeScript bug https://github.com/Rich-Harris/magic-string/pull/121\n\nexport default MagicString;\n"], "names": [], "mappings": ";;;;;;CAAe,MAAM,MAAM,CAAC;CAC5B,CAAC,WAAW,CAAC,GAAG,EAAE;CAClB,EAAE,IAAI,CAAC,IAAI,GAAG,GAAG,YAAY,MAAM,GAAG,GAAG,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC;CAC5D,EAAE;AACF;CACA,CAAC,GAAG,CAAC,CAAC,EAAE;CACR,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC;CACrC,EAAE;AACF;CACA,CAAC,GAAG,CAAC,CAAC,EAAE;CACR,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAC;CACjD,EAAE;CACF;;CCZe,MAAM,KAAK,CAAC;CAC3B,CAAC,WAAW,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE;CAClC,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAA;CACpB,EAAE,IAAI,CAAC,GAAG,GAAG,GAAG,CAAA;CAChB,EAAE,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAA;;CAEzB,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;CACjB,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;;CAEjB,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;CACxB,EAAE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;CACxB,EAAE,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;;CAErB,EAMS;CACT,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAA;CACvB,GAAG,IAAI,CAAC,IAAI,GAAG,IAAI,CAAA;CACnB,GAAE;CACF,EAAC;;CAED,CAAC,UAAU,CAAC,OAAO,EAAE;CACrB,EAAE,IAAI,CAAC,KAAK,IAAI,OAAO,CAAA;CACvB,EAAC;;CAED,CAAC,WAAW,CAAC,OAAO,EAAE;CACtB,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,CAAA;CACnC,EAAC;;CAED,CAAC,KAAK,GAAG;CACT,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;;CAE9D,EAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;CAC1B,EAAE,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;CAC1B,EAAE,KAAK,CAAC,OAAO,GAAG,IAAI,CAAC,OAAO,CAAA;CAC9B,EAAE,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAA;CAClC,EAAE,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAA;;CAE5B,EAAE,OAAO,KAAK,CAAA;CACd,EAAC;;CAED,CAAC,QAAQ,CAAC,KAAK,EAAE;CACjB,EAAE,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,GAAG,IAAI,CAAC,GAAG,CAAA;CAC/C,EAAC;;CAED,CAAC,QAAQ,CAAC,EAAE,EAAE;CACd,EAAE,IAAI,KAAK,GAAG,IAAI,CAAA;CAClB,EAAE,OAAO,KAAK,EAAE;CAChB,GAAG,EAAE,CAAC,KAAK,CAAC,CAAA;CACZ,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;CACrB,GAAE;CACF,EAAC;;CAED,CAAC,YAAY,CAAC,EAAE,EAAE;CAClB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAA;CAClB,EAAE,OAAO,KAAK,EAAE;CAChB,GAAG,EAAE,CAAC,KAAK,CAAC,CAAA;CACZ,GAAG,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAA;CACzB,GAAE;CACF,EAAC;;CAED,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE;CACvC,EAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;CACxB,EAAE,IAAI,CAAC,WAAW,EAAE;CACpB,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;CAClB,GAAG,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;CAClB,GAAE;CACF,EAAE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAA;;CAE5B,EAAE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAA;;CAEpB,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,WAAW,CAAC,OAAO,EAAE;CACtB,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;CACnC,EAAC;;CAED,CAAC,YAAY,CAAC,OAAO,EAAE;CACvB,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;CACnC,EAAC;;CAED,CAAC,KAAK,GAAG;CACT,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;CACjB,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;CACjB,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;CACnB,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAA;CAC/B,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAA;CACzB,GAAG,IAAI,CAAC,MAAM,GAAG,KAAK,CAAA;CACtB,GAAE;CACF,EAAC;;CAED,CAAC,KAAK,CAAC,KAAK,EAAE;CACd,EAAE,MAAM,UAAU,GAAG,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;;CAEvC,EAAE,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC,CAAA;CAC3D,EAAE,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;;CAEvD,EAAE,IAAI,CAAC,QAAQ,GAAG,cAAc,CAAA;;CAEhC,EAAE,MAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,aAAa,CAAC,CAAA;CAC5D,EAAE,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;CAC7B,EAAE,IAAI,CAAC,KAAK,GAAG,EAAE,CAAA;;CAEjB,EAAE,IAAI,CAAC,GAAG,GAAG,KAAK,CAAA;;CAElB,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE;CACnB;CACA;CACA;CACA;CACA;CACA;CACA;CACA;CACA,GAAG,QAAQ,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;CAC3B,GAAG,IAAI,CAAC,OAAO,GAAG,EAAE,CAAA;CACpB,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,OAAO,GAAG,cAAc,CAAA;CAChC,GAAE;;CAEF,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAA;CAC3B,EAAE,IAAI,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAA;CACtD,EAAE,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAA;CAC1B,EAAE,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAA;;CAEtB,EAAE,OAAO,QAAQ,CAAA;CACjB,EAAC;;CAED,CAAC,QAAQ,GAAG;CACZ,EAAE,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;CAC/C,EAAC;;CAED,CAAC,OAAO,CAAC,EAAE,EAAE;CACb,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;CACzC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;;CAEpC,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;;CAE9C,EAAE,IAAI,OAAO,CAAC,MAAM,EAAE;CACtB,GAAG,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;CACjC,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;CACrE,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;CACrB;CACA,KAAK,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;CAC7C,KAAI;CACJ,IAAG;CACH,GAAG,OAAO,IAAI,CAAA;CACd,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;;CAEjC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;CAC1C,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;CACrC,GAAE;CACF,EAAC;;CAED,CAAC,SAAS,CAAC,EAAE,EAAE;CACf,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;CACzC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;;CAEpC,EAAE,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;;CAE9C,EAAE,IAAI,OAAO,CAAC,MAAM,EAAE;CACtB,GAAG,IAAI,OAAO,KAAK,IAAI,CAAC,OAAO,EAAE;CACjC,IAAI,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,CAAC,CAAA;CAC1D,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;CACrB;CACA,KAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAA;CACjD,KAAI;CACJ,IAAI,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;CAClC,IAAG;CACH,GAAG,OAAO,IAAI,CAAA;CACd,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,CAAC,CAAA;;CAEjC,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;CAC1C,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;CACrC,GAAE;CACF,EAAC;CACD;;CCvLA,MAAM,KAAK,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;CAChC,MAAM,SAAS,GAAG,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;CACpC,MAAM,KAAK,GAAG,kEAAkE,CAAC;CACjF,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,EAAE,CAAC,CAAC;CACrC,MAAM,SAAS,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC,CAAC;CACtC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACvC,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;CAClC,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CACrB,IAAI,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;CACrB,CAAC;CAkBD,SAAS,aAAa,CAAC,OAAO,EAAE,GAAG,EAAE,QAAQ,EAAE;CAC/C,IAAI,IAAI,KAAK,GAAG,GAAG,GAAG,QAAQ,CAAC;CAC/B,IAAI,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC,GAAG,KAAK,IAAI,CAAC,CAAC;CACvD,IAAI,GAAG;CACP,QAAQ,IAAI,OAAO,GAAG,KAAK,GAAG,QAAQ,CAAC;CACvC,QAAQ,KAAK,MAAM,CAAC,CAAC;CACrB,QAAQ,IAAI,KAAK,GAAG,CAAC;CACrB,YAAY,OAAO,IAAI,QAAQ,CAAC;CAChC,QAAQ,OAAO,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC;CAC1C,KAAK,QAAQ,KAAK,GAAG,CAAC,EAAE;CACxB,IAAI,OAAO,GAAG,CAAC;CACf,CAAC;AAMD;CACA,MAAM,SAAS,GAAG,IAAI,GAAG,EAAE,CAAC;CAC5B;CACA,MAAM,EAAE,GAAG,OAAO,WAAW,KAAK,WAAW;CAC7C,sBAAsB,IAAI,WAAW,EAAE;CACvC,MAAM,OAAO,MAAM,KAAK,WAAW;CACnC,UAAU;CACV,YAAY,MAAM,CAAC,GAAG,EAAE;CACxB,gBAAgB,MAAM,GAAG,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,GAAG,CAAC,UAAU,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC;CACpF,gBAAgB,OAAO,GAAG,CAAC,QAAQ,EAAE,CAAC;CACtC,aAAa;CACb,SAAS;CACT,UAAU;CACV,YAAY,MAAM,CAAC,GAAG,EAAE;CACxB,gBAAgB,IAAI,GAAG,GAAG,EAAE,CAAC;CAC7B,gBAAgB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACrD,oBAAoB,GAAG,IAAI,MAAM,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;CACvD,iBAAiB;CACjB,gBAAgB,OAAO,GAAG,CAAC;CAC3B,aAAa;CACb,SAAS,CAAC;CACV,MAAM,YAAY,CAAC;CACnB,IAAI,WAAW,GAAG;CAClB,QAAQ,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;CACrB,QAAQ,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;CACtB,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,UAAU,CAAC,SAAS,CAAC,CAAC;CAChD,KAAK;CACL,IAAI,KAAK,CAAC,CAAC,EAAE;CACb,QAAQ,MAAM,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;CAChC,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC;CAC/B,QAAQ,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,EAAE;CACpC,YAAY,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;CAC1C,YAAY,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;CACzB,SAAS;CACT,KAAK;CACL,IAAI,KAAK,GAAG;CACZ,QAAQ,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;CAC1C,QAAQ,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC;CACxE,KAAK;CACL,CAAC;CAoTD,SAAS,MAAM,CAAC,OAAO,EAAE;CACzB,IAAI,MAAM,MAAM,GAAG,IAAI,YAAY,EAAE,CAAC;CACtC,IAAI,IAAI,YAAY,GAAG,CAAC,CAAC;CACzB,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;CACvB,IAAI,IAAI,YAAY,GAAG,CAAC,CAAC;CACzB,IAAI,IAAI,UAAU,GAAG,CAAC,CAAC;CACvB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC7C,QAAQ,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;CAChC,QAAQ,IAAI,CAAC,GAAG,CAAC;CACjB,YAAY,MAAM,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;CACpC,QAAQ,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC;CAC7B,YAAY,SAAS;CACrB,QAAQ,IAAI,SAAS,GAAG,CAAC,CAAC;CAC1B,QAAQ,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CAC9C,YAAY,MAAM,OAAO,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;CACpC,YAAY,IAAI,CAAC,GAAG,CAAC;CACrB,gBAAgB,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;CACpC,YAAY,SAAS,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,SAAS,CAAC,CAAC;CACrE,YAAY,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;CACpC,gBAAgB,SAAS;CACzB,YAAY,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;CAC3E,YAAY,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;CACvE,YAAY,YAAY,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC;CAC3E,YAAY,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC;CACpC,gBAAgB,SAAS;CACzB,YAAY,UAAU,GAAG,aAAa,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC;CACvE,SAAS;CACT,KAAK;CACL,IAAI,OAAO,MAAM,CAAC,KAAK,EAAE,CAAC;CAC1B;;CClaA,SAAS,OAAO,GAAG;CACnB,CAAC,IAAI,OAAO,UAAU,KAAK,WAAW,IAAI,OAAO,UAAU,CAAC,IAAI,KAAK,UAAU,EAAE;CACjF,EAAE,OAAO,CAAC,GAAG,KAAK,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;CACrE,EAAE,MAAM,IAAI,OAAO,MAAM,KAAK,UAAU,EAAE;CAC1C,EAAE,OAAO,CAAC,GAAG,KAAK,MAAM,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;CAC/D,EAAE,MAAM;CACR,EAAE,OAAO,MAAM;CACf,GAAG,MAAM,IAAI,KAAK,CAAC,yEAAyE,CAAC,CAAC;CAC9F,GAAG,CAAC;CACJ,EAAE;CACF,CAAC;AACD;CACA,MAAM,IAAI,iBAAiB,OAAO,EAAE,CAAC;AACrC;CACe,MAAM,SAAS,CAAC;CAC/B,CAAC,WAAW,CAAC,UAAU,EAAE;CACzB,EAAE,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;CACnB,EAAE,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;CAC9B,EAAE,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;CACpC,EAAE,IAAI,CAAC,cAAc,GAAG,UAAU,CAAC,cAAc,CAAC;CAClD,EAAE,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC;CAChC,EAAE,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;CAC9C,EAAE,IAAI,OAAO,UAAU,CAAC,mBAAmB,KAAK,WAAW,EAAE;CAC7D,GAAG,IAAI,CAAC,mBAAmB,GAAG,UAAU,CAAC,mBAAmB,CAAC;CAC7D,GAAG;CACH,EAAE,IAAI,OAAO,UAAU,CAAC,OAAO,KAAK,WAAW,EAAE;CACjD,GAAG,IAAI,CAAC,OAAO,GAAG,UAAU,CAAC,OAAO,CAAC;CACrC,GAAG;CACH,EAAE;AACF;CACA,CAAC,QAAQ,GAAG;CACZ,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;CAC9B,EAAE;AACF;CACA,CAAC,KAAK,GAAG;CACT,EAAE,OAAO,6CAA6C,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;CAC/E,EAAE;CACF;;CCvCe,SAAS,WAAW,CAAC,IAAI,EAAE;CAC1C,CAAC,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAChC;CACA,CAAC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;CAC1D,CAAC,MAAM,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;AAC5D;CACA,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;CACjD,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;AACF;CACA;CACA;CACA;CACA,CAAC,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,EAAE;CACrC,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;AACF;CACA;CACA,CAAC,MAAM,GAAG,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,OAAO,KAAK;CAClD,EAAE,MAAM,SAAS,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC;CAClD,EAAE,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;CACvC,EAAE,EAAE,QAAQ,CAAC,CAAC;AACd;CACA,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACrC;;CCxBe,SAAS,eAAe,CAAC,IAAI,EAAE,EAAE,EAAE;CAClD,CAAC,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;CACvC,CAAC,MAAM,OAAO,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;AACnC;CACA,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;AACjB;CACA,CAAC,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,EAAE;CACrC,EAAE,SAAS,CAAC,KAAK,EAAE,CAAC;CACpB,EAAE,OAAO,CAAC,KAAK,EAAE,CAAC;CAClB,EAAE;AACF;CACA,CAAC,IAAI,SAAS,CAAC,MAAM,EAAE;CACvB,EAAE,IAAI,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;CAC3B,EAAE,OAAO,CAAC,EAAE,EAAE,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC;CAClC,EAAE;AACF;CACA,CAAC,OAAO,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CAC5C;;CCjBA,MAAM,QAAQ,GAAG,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC;AAC3C;CACe,SAAS,QAAQ,CAAC,KAAK,EAAE;CACxC,CAAC,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,iBAAiB,CAAC;CACnD;;CCJe,SAAS,UAAU,CAAC,MAAM,EAAE;CAC3C,CAAC,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;CAC1C,CAAC,MAAM,WAAW,GAAG,EAAE,CAAC;AACxB;CACA,CAAC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;CACzD,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;CACxB,EAAE,GAAG,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC;CACrC,EAAE;AACF;CACA,CAAC,OAAO,SAAS,MAAM,CAAC,KAAK,EAAE;CAC/B,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC;CACZ,EAAE,IAAI,CAAC,GAAG,WAAW,CAAC,MAAM,CAAC;CAC7B,EAAE,OAAO,CAAC,GAAG,CAAC,EAAE;CAChB,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;CAC1B,GAAG,IAAI,KAAK,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE;CAC/B,IAAI,CAAC,GAAG,CAAC,CAAC;CACV,IAAI,MAAM;CACV,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;CACd,IAAI;CACJ,GAAG;CACH,EAAE,MAAM,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;CACrB,EAAE,MAAM,MAAM,GAAG,KAAK,GAAG,WAAW,CAAC,IAAI,CAAC,CAAC;CAC3C,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC;CAC1B,EAAE,CAAC;CACH;;CCxBA,MAAM,SAAS,GAAG,IAAI,CAAC;AACvB;CACe,MAAM,QAAQ,CAAC;CAC9B,CAAC,WAAW,CAAC,KAAK,EAAE;CACpB,EAAE,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;CACrB,EAAE,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC;CAC7B,EAAE,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;CAC/B,EAAE,IAAI,CAAC,GAAG,GAAG,EAAE,CAAC;CAChB,EAAE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,EAAE,CAAC;CAC3D,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;CACtB,EAAE;AACF;CACA,CAAC,OAAO,CAAC,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,SAAS,EAAE;CAC/C,EAAE,IAAI,OAAO,CAAC,MAAM,EAAE;CACtB,GAAG,MAAM,qBAAqB,GAAG,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;CACpD,GAAG,IAAI,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC;CACjD,GAAG,IAAI,sBAAsB,GAAG,CAAC,CAAC,CAAC;CACnC;CACA;CACA,GAAG,OAAO,cAAc,IAAI,CAAC,IAAI,qBAAqB,GAAG,cAAc,EAAE;CACzE,IAAI,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;CAClF,IAAI,IAAI,SAAS,IAAI,CAAC,EAAE;CACxB,KAAK,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;CAC7B,KAAK;CACL,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AACnC;CACA,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;CAChC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;CAC7D,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;AACjC;CACA,IAAI,sBAAsB,GAAG,cAAc,CAAC;CAC5C,IAAI,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,EAAE,cAAc,GAAG,CAAC,CAAC,CAAC;CAC/D,IAAI;AACJ;CACA,GAAG,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;CACjF,GAAG,IAAI,SAAS,IAAI,CAAC,EAAE;CACvB,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;CAC5B,IAAI;CACJ,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;AAClC;CACA,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,CAAC,CAAC,CAAC;CAC3D,GAAG,MAAM,IAAI,IAAI,CAAC,OAAO,EAAE;CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;CACvC,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;CACzB,GAAG;AACH;CACA,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;CACtB,EAAE;AACF;CACA,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,kBAAkB,EAAE;CACzE,EAAE,IAAI,iBAAiB,GAAG,KAAK,CAAC,KAAK,CAAC;CACtC,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC;CACnB;CACA,EAAE,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAClC;CACA,EAAE,OAAO,iBAAiB,GAAG,KAAK,CAAC,GAAG,EAAE;CACxC,GAAG,IAAI,QAAQ,CAAC,iBAAiB,CAAC,KAAK,IAAI,EAAE;CAC7C,IAAI,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC;CAClB,IAAI,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC;CACnB,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,CAAC;CAChC,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;CAC7D,IAAI,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;CACjC,IAAI,KAAK,GAAG,IAAI,CAAC;CACjB,IAAI,mBAAmB,GAAG,KAAK,CAAC;CAChC,IAAI,MAAM;CACV,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK,IAAI,kBAAkB,CAAC,GAAG,CAAC,iBAAiB,CAAC,EAAE;CAC1E,KAAK,MAAM,OAAO,GAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE,WAAW,EAAE,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC;AACnF;CACA,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,UAAU,EAAE;CACpC;CACA,MAAM,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,CAAC,EAAE;CACvD;CACA,OAAO,IAAI,CAAC,mBAAmB,EAAE;CACjC,QAAQ,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;CACvC,QAAQ,mBAAmB,GAAG,IAAI,CAAC;CACnC,QAAQ;CACR,OAAO,MAAM;CACb;CACA,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;CACtC,OAAO,mBAAmB,GAAG,KAAK,CAAC;CACnC,OAAO;CACP,MAAM,MAAM;CACZ,MAAM,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;CACrC,MAAM;CACN,KAAK;AACL;CACA,IAAI,GAAG,CAAC,MAAM,IAAI,CAAC,CAAC;CACpB,IAAI,IAAI,CAAC,mBAAmB,IAAI,CAAC,CAAC;CAClC,IAAI,KAAK,GAAG,KAAK,CAAC;CAClB,IAAI;AACJ;CACA,GAAG,iBAAiB,IAAI,CAAC,CAAC;CAC1B,GAAG;AACH;CACA,EAAE,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;CACtB,EAAE;AACF;CACA,CAAC,OAAO,CAAC,GAAG,EAAE;CACd,EAAE,IAAI,CAAC,GAAG,EAAE,OAAO;AACnB;CACA,EAAE,MAAM,KAAK,GAAG,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;AAChC;CACA,EAAE,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;CACxB,GAAG,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;CAC9C,IAAI,IAAI,CAAC,iBAAiB,EAAE,CAAC;CAC7B,IAAI,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;CAC7D,IAAI;CACJ,GAAG,IAAI,CAAC,mBAAmB,GAAG,CAAC,CAAC;CAChC,GAAG;AACH;CACA,EAAE,IAAI,CAAC,mBAAmB,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC;CAC7D,EAAE;CACF;;CCtGA,MAAM,CAAC,GAAG,IAAI,CAAA;;CAEd,MAAM,MAAM,GAAG;CACf,CAAC,UAAU,EAAE,KAAK;CAClB,CAAC,WAAW,EAAE,KAAK;CACnB,CAAC,SAAS,EAAE,KAAK;CACjB,CAAC,CAAA;;CAEc,MAAM,WAAW,CAAC;CACjC,CAAC,WAAW,CAAC,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE;CACnC,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;;CAEnD,EAAE,MAAM,CAAC,gBAAgB,CAAC,IAAI,EAAE;CAChC,GAAG,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE;CAC9C,GAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;CACvC,GAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;CACvC,GAAG,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;CAC/C,GAAG,SAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;CAC9C,GAAG,iBAAiB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE;CACtD,GAAG,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;CACzC,GAAG,KAAK,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;CACvC,GAAG,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,QAAQ,EAAE;CACxD,GAAG,qBAAqB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,qBAAqB,EAAE;CAClF,GAAG,kBAAkB,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,MAAM,EAAE,EAAE;CAC9D,GAAG,WAAW,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,EAAE,EAAE;CAC7C,GAAG,SAAS,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE;CAClD,GAAG,UAAU,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,UAAU,EAAE;CAC5D,GAAG,MAAM,EAAE,EAAE,QAAQ,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE;CACzD,GAAG,CAAC,CAAA;;CAMJ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK,CAAA;CACzB,EAAE,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,GAAG,KAAK,CAAA;CACnC,EAAC;;CAED,CAAC,oBAAoB,CAAC,IAAI,EAAE;CAC5B,EAAE,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAA;CACnC,EAAC;;CAED,CAAC,MAAM,CAAC,OAAO,EAAE;CACjB,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,CAAA;;CAExF,EAAE,IAAI,CAAC,KAAK,IAAI,OAAO,CAAA;CACvB,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE;CAC5B,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;;CAE7B,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAA;;CAI3F,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;;CAEpB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;;CAEjC,EAAE,IAAI,KAAK,EAAE;CACb,GAAG,KAAK,CAAC,UAAU,CAAC,OAAO,CAAC,CAAA;CAC5B,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,KAAK,IAAI,OAAO,CAAA;CACxB,GAAE;CAGF,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE;CAC7B,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;;CAE7B,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAA;;CAI3F,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;;CAEpB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;;CAEnC,EAAE,IAAI,KAAK,EAAE;CACb,GAAG,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;CAC7B,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,KAAK,IAAI,OAAO,CAAA;CACxB,GAAE;CAGF,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,KAAK,GAAG;CACT,EAAE,MAAM,MAAM,GAAG,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,EAAE,EAAE,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAA;;CAEjG,EAAE,IAAI,aAAa,GAAG,IAAI,CAAC,UAAU,CAAA;CACrC,EAAE,IAAI,WAAW,IAAI,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,iBAAiB,GAAG,aAAa,CAAC,KAAK,EAAE,CAAC,CAAA;;CAE1F,EAAE,OAAO,aAAa,EAAE;CACxB,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,WAAW,CAAA;CAClD,GAAG,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW,CAAA;;CAE9C,GAAG,MAAM,iBAAiB,GAAG,aAAa,CAAC,IAAI,CAAA;CAC/C,GAAG,MAAM,eAAe,GAAG,iBAAiB,IAAI,iBAAiB,CAAC,KAAK,EAAE,CAAA;;CAEzE,GAAG,IAAI,eAAe,EAAE;CACxB,IAAI,WAAW,CAAC,IAAI,GAAG,eAAe,CAAA;CACtC,IAAI,eAAe,CAAC,QAAQ,GAAG,WAAW,CAAA;;CAE1C,IAAI,WAAW,GAAG,eAAe,CAAA;CACjC,IAAG;;CAEH,GAAG,aAAa,GAAG,iBAAiB,CAAA;CACpC,GAAE;;CAEF,EAAE,MAAM,CAAC,SAAS,GAAG,WAAW,CAAA;;CAEhC,EAAE,IAAI,IAAI,CAAC,qBAAqB,EAAE;CAClC,GAAG,MAAM,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,EAAE,CAAA;CACpE,GAAE;;CAEF,EAAE,MAAM,CAAC,kBAAkB,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAA;;CAEjE,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;CAC3B,EAAE,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAA;;CAE3B,EAAE,OAAO,MAAM,CAAA;CACf,EAAC;;CAED,CAAC,kBAAkB,CAAC,OAAO,EAAE;CAC7B,EAAE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;;CAEzB,EAAE,MAAM,WAAW,GAAG,CAAC,CAAA;CACvB,EAAE,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAA;CAC7C,EAAE,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;;CAE9C,EAAE,MAAM,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;;CAE1C,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE;CAClB,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;CAC/B,GAAE;;CAEF,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,KAAK,KAAK;CACtC,GAAG,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;;CAElC,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;;CAExD,GAAG,IAAI,KAAK,CAAC,MAAM,EAAE;CACrB,IAAI,QAAQ,CAAC,OAAO;CACpB,KAAK,WAAW;CAChB,KAAK,KAAK,CAAC,OAAO;CAClB,KAAK,GAAG;CACR,KAAK,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;CACzD,KAAK,CAAA;CACL,IAAI,MAAM;CACV,IAAI,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAA;CAC9F,IAAG;;CAEH,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;CACxD,GAAG,CAAC,CAAA;;CAEJ,EAAE,OAAO;CACT,GAAG,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,SAAS;CACrE,GAAG,OAAO,EAAE;CACZ,IAAI,OAAO,CAAC,MAAM,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,EAAE,OAAO,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,IAAI,EAAE;CAC7F,IAAI;CACJ,GAAG,cAAc,EAAE,OAAO,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,SAAS;CACvE,GAAG,KAAK;CACR,GAAG,QAAQ,EAAE,QAAQ,CAAC,GAAG;CACzB,GAAG,mBAAmB,EAAE,IAAI,CAAC,UAAU,GAAG,CAAC,WAAW,CAAC,GAAG,SAAS;CACnE,GAAG,CAAA;CACH,EAAC;;CAED,CAAC,WAAW,CAAC,OAAO,EAAE;CACtB,EAAE,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAA;CACxD,EAAC;;CAED,CAAC,gBAAgB,GAAG;CACpB,EAAE,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;CACpC,GAAG,IAAI,CAAC,SAAS,GAAG,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;CAC9C,GAAE;CACF,EAAC;;CAED,CAAC,mBAAmB,GAAG;CACvB,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAA;CACzB,EAAE,OAAO,IAAI,CAAC,SAAS,CAAA;CACvB,EAAC;;CAED,CAAC,eAAe,GAAG;CACnB,EAAE,IAAI,CAAC,gBAAgB,EAAE,CAAA;CACzB,EAAE,OAAO,IAAI,CAAC,SAAS,KAAK,IAAI,GAAG,IAAI,GAAG,IAAI,CAAC,SAAS,CAAA;CACxD,EAAC;;CAED,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE;CAC5B,EAAE,MAAM,OAAO,GAAG,YAAY,CAAA;;CAE9B,EAAE,IAAI,QAAQ,CAAC,SAAS,CAAC,EAAE;CAC3B,GAAG,OAAO,GAAG,SAAS,CAAA;CACtB,GAAG,SAAS,GAAG,SAAS,CAAA;CACxB,GAAE;;CAEF,EAAE,IAAI,SAAS,KAAK,SAAS,EAAE;CAC/B,GAAG,IAAI,CAAC,gBAAgB,EAAE,CAAA;CAC1B,GAAG,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,IAAI,CAAA;CACrC,GAAE;;CAEF,EAAE,IAAI,SAAS,KAAK,EAAE,EAAE,OAAO,IAAI,CAAC;;CAEpC,EAAE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;;CAEzB;CACA,EAAE,MAAM,UAAU,GAAG,EAAE,CAAA;;CAEvB,EAAE,IAAI,OAAO,CAAC,OAAO,EAAE;CACvB,GAAG,MAAM,UAAU;CACnB,IAAI,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,QAAQ,GAAG,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC,OAAO,CAAA;CAChF,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,SAAS,KAAK;CACrC,IAAI,KAAK,IAAI,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE;CACzD,KAAK,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI,CAAA;CACzB,KAAI;CACJ,IAAI,CAAC,CAAA;CACL,GAAE;;CAEF,EAAE,IAAI,yBAAyB,GAAG,OAAO,CAAC,WAAW,KAAK,KAAK,CAAA;CAC/D,EAAE,MAAM,QAAQ,GAAG,CAAC,KAAK,KAAK;CAC9B,GAAG,IAAI,yBAAyB,EAAE,OAAO,CAAC,EAAE,SAAS,CAAC,EAAE,KAAK,CAAC,CAAC,CAAA;CAC/D,GAAG,yBAAyB,GAAG,IAAI,CAAA;CACnC,GAAG,OAAO,KAAK,CAAA;CACf,GAAG,CAAA;;CAEH,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;;CAEpD,EAAE,IAAI,SAAS,GAAG,CAAC,CAAA;CACnB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;;CAE7B,EAAE,OAAO,KAAK,EAAE;CAChB,GAAG,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAA;;CAExB,GAAG,IAAI,KAAK,CAAC,MAAM,EAAE;CACrB,IAAI,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;CAChC,KAAK,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;;CAE7D,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;CAC/B,MAAM,yBAAyB,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,IAAI,CAAA;CAClF,MAAK;CACL,KAAI;CACJ,IAAI,MAAM;CACV,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAA;;CAE3B,IAAI,OAAO,SAAS,GAAG,GAAG,EAAE;CAC5B,KAAK,IAAI,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;CACjC,MAAM,MAAM,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;;CAE3C,MAAM,IAAI,IAAI,KAAK,IAAI,EAAE;CACzB,OAAO,yBAAyB,GAAG,IAAI,CAAA;CACvC,OAAO,MAAM,IAAI,IAAI,KAAK,IAAI,IAAI,yBAAyB,EAAE;CAC7D,OAAO,yBAAyB,GAAG,KAAK,CAAA;;CAExC,OAAO,IAAI,SAAS,KAAK,KAAK,CAAC,KAAK,EAAE;CACtC,QAAQ,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;CACrC,QAAQ,MAAM;CACd,QAAQ,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,SAAS,CAAC,CAAA;CAC1C,QAAQ,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;CAC1B,QAAQ,KAAK,CAAC,YAAY,CAAC,SAAS,CAAC,CAAA;CACrC,QAAO;CACP,OAAM;CACN,MAAK;;CAEL,KAAK,SAAS,IAAI,CAAC,CAAA;CACnB,KAAI;CACJ,IAAG;;CAEH,GAAG,SAAS,GAAG,KAAK,CAAC,GAAG,CAAA;CACxB,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;CACrB,GAAE;;CAEF,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAA;;CAEpD,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,MAAM,GAAG;CACV,EAAE,MAAM,IAAI,KAAK;CACjB,GAAG,iFAAiF;CACpF,GAAG,CAAA;CACH,EAAC;;CAED,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,EAAE;CAC5B,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE;CAC1B,GAAG,OAAO,CAAC,IAAI;CACf,IAAI,oFAAoF;CACxF,IAAI,CAAA;CACJ,GAAG,MAAM,CAAC,UAAU,GAAG,IAAI,CAAA;CAC3B,GAAE;;CAEF,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;CACxC,EAAC;;CAED,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE;CAC7B,EAAE,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;CAC3B,GAAG,OAAO,CAAC,IAAI;CACf,IAAI,uFAAuF;CAC3F,IAAI,CAAA;CACJ,GAAG,MAAM,CAAC,WAAW,GAAG,IAAI,CAAA;CAC5B,GAAE;;CAEF,EAAE,OAAO,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,CAAC,CAAA;CAC1C,EAAC;;CAED,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,EAAE;CACzB,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;CAC7B,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;CACzB,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;;CAE7B,EAAE,IAAI,KAAK,IAAI,KAAK,IAAI,KAAK,IAAI,GAAG,EAAE,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAA;;CAI9F,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;CACpB,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;CAClB,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;;CAEpB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;CACnC,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;;CAE9B,EAAE,MAAM,OAAO,GAAG,KAAK,CAAC,QAAQ,CAAA;CAChC,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAA;;CAE5B,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;CACtC,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,KAAK,IAAI,CAAC,SAAS,EAAE,OAAO,IAAI,CAAA;CACvD,EAAE,MAAM,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAA;;CAE/D,EAAE,IAAI,OAAO,EAAE,OAAO,CAAC,IAAI,GAAG,QAAQ,CAAA;CACtC,EAAE,IAAI,QAAQ,EAAE,QAAQ,CAAC,QAAQ,GAAG,OAAO,CAAA;;CAE3C,EAAE,IAAI,OAAO,EAAE,OAAO,CAAC,IAAI,GAAG,KAAK,CAAA;CACnC,EAAE,IAAI,QAAQ,EAAE,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAA;;CAExC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI,CAAA;CAClD,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;CAClB,GAAG,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAA;CAClC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,GAAG,IAAI,CAAA;CAC7B,GAAE;;CAEF,EAAE,KAAK,CAAC,QAAQ,GAAG,OAAO,CAAA;CAC1B,EAAE,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAA;;CAE9B,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,GAAG,KAAK,CAAA;CACvC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;CAGtC,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,SAAS,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE;CACzC,EAAE,OAAO,GAAG,OAAO,IAAI,EAAE,CAAA;CACzB,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,GAAG,OAAO,EAAE,SAAS,EAAE,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAA;CAC1F,EAAC;;CAED,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE;CACtC,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;CAC7B,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;;CAEzB,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,sCAAsC,CAAC,CAAA;;CAE9F,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;CAClC,GAAG,OAAO,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;CAClD,GAAG,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;CAC9C,GAAE;;CAEF,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;CACzE,EAAE,IAAI,KAAK,KAAK,GAAG;CACnB,GAAG,MAAM,IAAI,KAAK;CAClB,IAAI,+EAA+E;CACnF,IAAI,CAAA;;CAIJ,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;CACpB,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;;CAElB,EAAE,IAAI,OAAO,KAAK,IAAI,EAAE;CACxB,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;CAC1B,IAAI,OAAO,CAAC,IAAI;CAChB,KAAK,+HAA+H;CACpI,KAAK,CAAA;CACL,IAAI,MAAM,CAAC,SAAS,GAAG,IAAI,CAAA;CAC3B,IAAG;;CAEH,GAAG,OAAO,GAAG,EAAE,SAAS,EAAE,IAAI,EAAE,CAAA;CAChC,GAAE;CACF,EAAE,MAAM,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAA;CACrE,EAAE,MAAM,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,KAAK,CAAA;;CAErE,EAAE,IAAI,SAAS,EAAE;CACjB,GAAG,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAA;CACnD,GAAG,MAAM,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE,QAAQ,EAAE;CACrD,IAAI,QAAQ,EAAE,IAAI;CAClB,IAAI,KAAK,EAAE,IAAI;CACf,IAAI,UAAU,EAAE,IAAI;CACpB,IAAI,CAAC,CAAA;CACL,GAAE;;CAEF,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;CACnC,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;;CAE9B,EAAE,IAAI,KAAK,EAAE;CACb,GAAG,IAAI,KAAK,GAAG,KAAK,CAAA;CACpB,GAAG,OAAO,KAAK,KAAK,IAAI,EAAE;CAC1B,IAAI,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE;CAChD,KAAK,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAA;CAC7D,KAAI;CACJ,IAAI,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;CACtB,IAAI,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,KAAK,CAAC,CAAA;CACzB,IAAG;;CAEH,GAAG,KAAK,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,SAAS,CAAC,CAAA;CAC7C,GAAG,MAAM;CACT;CACA,GAAG,MAAM,QAAQ,GAAG,IAAI,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,CAAC,CAAA;;CAEtE;CACA,GAAG,IAAI,CAAC,IAAI,GAAG,QAAQ,CAAA;CACvB,GAAG,QAAQ,CAAC,QAAQ,GAAG,IAAI,CAAA;CAC3B,GAAE;CAGF,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,OAAO,CAAC,OAAO,EAAE;CAClB,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,gCAAgC,CAAC,CAAA;;CAExF,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;CACnC,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE;CAC7B,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;;CAE7B,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAA;;CAI3F,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;;CAEpB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;;CAEjC,EAAE,IAAI,KAAK,EAAE;CACb,GAAG,KAAK,CAAC,WAAW,CAAC,OAAO,CAAC,CAAA;CAC7B,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;CACpC,GAAE;CAGF,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,YAAY,CAAC,KAAK,EAAE,OAAO,EAAE;CAC9B,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;;CAE7B,EAAE,IAAI,OAAO,OAAO,KAAK,QAAQ,EAAE,MAAM,IAAI,SAAS,CAAC,mCAAmC,CAAC,CAAA;;CAI3F,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;;CAEpB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;;CAEnC,EAAE,IAAI,KAAK,EAAE;CACb,GAAG,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;CAC9B,GAAG,MAAM;CACT,GAAG,IAAI,CAAC,KAAK,GAAG,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;CACpC,GAAE;CAGF,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE;CACpB,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;CAC7B,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;;CAEzB,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;CAClC,GAAG,OAAO,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;CAClD,GAAG,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;CAC9C,GAAE;;CAEF,EAAE,IAAI,KAAK,KAAK,GAAG,EAAE,OAAO,IAAI,CAAA;;CAEhC,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;CAC5F,EAAE,IAAI,KAAK,GAAG,GAAG,EAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;;CAIpE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;CACpB,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;;CAElB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;;CAEjC,EAAE,OAAO,KAAK,EAAE;CAChB,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE,CAAA;CACnB,GAAG,KAAK,CAAC,KAAK,GAAG,EAAE,CAAA;CACnB,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;;CAEjB,GAAG,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;CAC3D,GAAE;CAGF,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE;CACnB,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;CAC7B,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;;CAEzB,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;CAClC,GAAG,OAAO,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;CAClD,GAAG,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;CAC9C,GAAE;;CAEF,EAAE,IAAI,KAAK,KAAK,GAAG,EAAE,OAAO,IAAI,CAAA;;CAEhC,EAAE,IAAI,KAAK,GAAG,CAAC,IAAI,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;CAC5F,EAAE,IAAI,KAAK,GAAG,GAAG,EAAE,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAA;;CAIpE,EAAE,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;CACpB,EAAE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;;CAElB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;;CAEjC,EAAE,OAAO,KAAK,EAAE;CAChB,GAAG,KAAK,CAAC,KAAK,EAAE,CAAA;;CAEhB,GAAG,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAA;CAC3D,GAAE;CAGF,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,QAAQ,GAAG;CACZ,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;CACjE,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;CAC5B,EAAE,GAAG;CACL,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;CACrE,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;CAC3E,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;CACrE,GAAG,SAAS,KAAK,GAAG,KAAK,CAAC,QAAQ,GAAE;CACpC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;CACjE,EAAE,OAAO,EAAE,CAAA;CACX,EAAC;;CAED,CAAC,QAAQ,GAAG;CACZ,EAAE,IAAI,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;CAC3C,EAAE,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,CAAA;CAC/D,EAAE,IAAI,OAAO,GAAG,IAAI,CAAC,KAAK,CAAA;CAC1B,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;CAC5B,EAAE,GAAG;CACL,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;CAC/B,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;CAC1C,IAAI,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAA;CAC5E,IAAI,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,CAAA;CACnC,IAAG;;CAEH,GAAG,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;CACjC,IAAI,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;CAC5C,IAAI,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAA;CAC9E,IAAI,OAAO,GAAG,KAAK,CAAC,OAAO,GAAG,OAAO,CAAA;CACrC,IAAG;;CAEH,GAAG,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;CAC/B,IAAI,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;CAC1C,IAAI,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,KAAK,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAA;CAC5E,IAAI,OAAO,GAAG,KAAK,CAAC,KAAK,GAAG,OAAO,CAAA;CACnC,IAAG;CACH,GAAG,SAAS,KAAK,GAAG,KAAK,CAAC,QAAQ,GAAE;CACpC,EAAE,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;CACvC,EAAE,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,GAAG,CAAC,CAAC,GAAG,OAAO,CAAA;CACzE,EAAE,OAAO,IAAI,CAAC,KAAK,GAAG,OAAO,CAAA;CAC7B,EAAC;;CAED,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,EAAE,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,EAAE;CAC5D,EAAE,KAAK,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAA;CAC7B,EAAE,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,MAAM,CAAA;;CAEzB,EAAE,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;CAClC,GAAG,OAAO,KAAK,GAAG,CAAC,EAAE,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;CAClD,GAAG,OAAO,GAAG,GAAG,CAAC,EAAE,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAA;CAC9C,GAAE;;CAEF,EAAE,IAAI,MAAM,GAAG,EAAE,CAAA;;CAEjB;CACA,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;CAC7B,EAAE,OAAO,KAAK,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC,EAAE;CAC/D;CACA,GAAG,IAAI,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,EAAE;CAC9C,IAAI,OAAO,MAAM,CAAA;CACjB,IAAG;;CAEH,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;CACrB,GAAE;;CAEF,EAAE,IAAI,KAAK,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK;CACpD,GAAG,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,KAAK,CAAC,uBAAuB,CAAC,CAAC,CAAA;;CAEnF,EAAE,MAAM,UAAU,GAAG,KAAK,CAAA;CAC1B,EAAE,OAAO,KAAK,EAAE;CAChB,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,UAAU,KAAK,KAAK,IAAI,KAAK,CAAC,KAAK,KAAK,KAAK,CAAC,EAAE;CACvE,IAAI,MAAM,IAAI,KAAK,CAAC,KAAK,CAAA;CACzB,IAAG;;CAEH,GAAG,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,GAAG,GAAG,IAAI,KAAK,CAAC,GAAG,IAAI,GAAG,CAAA;CAC5D,GAAG,IAAI,WAAW,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG;CACvD,IAAI,MAAM,IAAI,KAAK,CAAC,CAAC,8BAA8B,EAAE,GAAG,CAAC,qBAAqB,CAAC,CAAC,CAAA;;CAEhF,GAAG,MAAM,UAAU,GAAG,UAAU,KAAK,KAAK,GAAG,KAAK,GAAG,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA;CACpE,GAAG,MAAM,QAAQ,GAAG,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,CAAC,GAAG,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAA;;CAE/F,GAAG,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,CAAC,CAAA;;CAEtD,GAAG,IAAI,KAAK,CAAC,KAAK,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,CAAC,EAAE;CAC3D,IAAI,MAAM,IAAI,KAAK,CAAC,KAAK,CAAA;CACzB,IAAG;;CAEH,GAAG,IAAI,WAAW,EAAE;CACpB,IAAI,MAAK;CACT,IAAG;;CAEH,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;CACrB,GAAE;;CAEF,EAAE,OAAO,MAAM,CAAA;CACf,EAAC;;CAED;CACA,CAAC,IAAI,CAAC,KAAK,EAAE,GAAG,EAAE;CAClB,EAAE,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,EAAE,CAAA;CAC5B,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;CACxB,EAAE,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA;;CAE1C,EAAE,OAAO,KAAK,CAAA;CACd,EAAC;;CAED,CAAC,MAAM,CAAC,KAAK,EAAE;CACf,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE,OAAM;;CAItD,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,iBAAiB,CAAA;CACpC,EAAE,MAAM,aAAa,GAAG,KAAK,GAAG,KAAK,CAAC,GAAG,CAAA;;CAEzC,EAAE,OAAO,KAAK,EAAE;CAChB,GAAG,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;;CAEnE,GAAG,KAAK,GAAG,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;CAC5E,GAAE;CACF,EAAC;;CAED,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE;CAC3B,EAAE,IAAI,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,EAAE;CAC5C;CACA,GAAG,MAAM,GAAG,GAAG,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,KAAK,CAAC,CAAA;CAC/C,GAAG,MAAM,IAAI,KAAK;CAClB,IAAI,CAAC,mDAAmD,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;CACzG,IAAI,CAAA;CACJ,GAAE;;CAEF,EAAE,MAAM,QAAQ,GAAG,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;;CAErC,EAAE,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAA;CAC3B,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,QAAQ,CAAA;CAChC,EAAE,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAA;;CAErC,EAAE,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAA;;CAEzD,EAAE,IAAI,CAAC,iBAAiB,GAAG,KAAK,CAAA;CAEhC,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,QAAQ,GAAG;CACZ,EAAE,IAAI,GAAG,GAAG,IAAI,CAAC,KAAK,CAAA;;CAEtB,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;CAC7B,EAAE,OAAO,KAAK,EAAE;CAChB,GAAG,GAAG,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAA;CAC1B,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;CACrB,GAAE;;CAEF,EAAE,OAAO,GAAG,GAAG,IAAI,CAAC,KAAK,CAAA;CACzB,EAAC;;CAED,CAAC,OAAO,GAAG;CACX,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;CAC7B,EAAE,GAAG;CACL,GAAG;CACH,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE;CAC7C,KAAK,KAAK,CAAC,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;CAClD,KAAK,KAAK,CAAC,KAAK,CAAC,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAA;CAC7C;CACA,IAAI,OAAO,KAAK,CAAA;CAChB,GAAG,SAAS,KAAK,GAAG,KAAK,CAAC,IAAI,GAAE;CAChC,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,MAAM,GAAG;CACV,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;CAC7B,EAAE,IAAI,MAAM,GAAG,CAAC,CAAA;CAChB,EAAE,GAAG;CACL,GAAG,MAAM,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,CAAA;CAC3E,GAAG,SAAS,KAAK,GAAG,KAAK,CAAC,IAAI,GAAE;CAChC,EAAE,OAAO,MAAM,CAAA;CACf,EAAC;;CAED,CAAC,SAAS,GAAG;CACb,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;CAC9B,EAAC;;CAED,CAAC,IAAI,CAAC,QAAQ,EAAE;CAChB,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;CACnD,EAAC;;CAED,CAAC,cAAc,CAAC,QAAQ,EAAE;CAC1B,EAAE,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,CAAA;;CAEnD,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;CACzC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;;CAEpC,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,SAAS,CAAA;;CAE5B,EAAE,GAAG;CACL,GAAG,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAA;CACxB,GAAG,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC,EAAE,CAAC,CAAA;;CAEpC;CACA,GAAG,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;CAC1B,IAAI,IAAI,IAAI,CAAC,SAAS,KAAK,KAAK,EAAE;CAClC,KAAK,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAA;CAChC,KAAI;;CAEJ,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;CACjC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAA;CAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAA;CAC3C,IAAG;;CAEH,GAAG,IAAI,OAAO,EAAE,OAAO,IAAI,CAAA;CAC3B,GAAG,KAAK,GAAG,KAAK,CAAC,QAAQ,CAAA;CACzB,GAAG,QAAQ,KAAK,EAAC;;CAEjB,EAAE,OAAO,KAAK,CAAA;CACd,EAAC;;CAED,CAAC,OAAO,CAAC,QAAQ,EAAE;CACnB,EAAE,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAA;CAC/B,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;CACD,CAAC,gBAAgB,CAAC,QAAQ,EAAE;CAC5B,EAAE,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAA;;CAExD,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;CACzC,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,OAAO,IAAI,CAAA;;CAEpC,EAAE,IAAI,KAAK,GAAG,IAAI,CAAC,UAAU,CAAA;;CAE7B,EAAE,GAAG;CACL,GAAG,MAAM,GAAG,GAAG,KAAK,CAAC,GAAG,CAAA;CACxB,GAAG,MAAM,OAAO,GAAG,KAAK,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;;CAEtC,GAAG,IAAI,KAAK,CAAC,GAAG,KAAK,GAAG,EAAE;CAC1B;CACA,IAAI,IAAI,KAAK,KAAK,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,IAAI,CAAA;;CAE7D,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;CACjC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,CAAA;CAC/C,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,IAAI,CAAA;CAC3C,IAAG;;CAEH,GAAG,IAAI,OAAO,EAAE,OAAO,IAAI,CAAA;CAC3B,GAAG,KAAK,GAAG,KAAK,CAAC,IAAI,CAAA;CACrB,GAAG,QAAQ,KAAK,EAAC;;CAEjB,EAAE,OAAO,KAAK,CAAA;CACd,EAAC;;CAED,CAAC,SAAS,CAAC,QAAQ,EAAE;CACrB,EAAE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAA;CACjC,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,UAAU,GAAG;CACd,EAAE,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAA;CAC1C,EAAC;;CAED,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,EAAE;CAC1C,EAAE,SAAS,cAAc,CAAC,KAAK,EAAE,GAAG,EAAE;CACtC,GAAG,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;CACxC,IAAI,OAAO,WAAW,CAAC,OAAO,CAAC,eAAe,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK;CAC1D;CACA,KAAK,IAAI,CAAC,KAAK,GAAG,EAAE,OAAO,GAAG,CAAA;CAC9B,KAAK,IAAI,CAAC,KAAK,GAAG,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAA;CACnC,KAAK,MAAM,GAAG,GAAG,CAAC,CAAC,CAAA;CACnB,KAAK,IAAI,GAAG,GAAG,KAAK,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;CAC7C,KAAK,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;CACnB,KAAK,CAAC,CAAA;CACN,IAAI,MAAM;CACV,IAAI,OAAO,WAAW,CAAC,GAAG,KAAK,EAAE,KAAK,CAAC,KAAK,EAAE,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,CAAA;CAChE,IAAG;CACH,GAAE;CACF,EAAE,SAAS,QAAQ,CAAC,EAAE,EAAE,GAAG,EAAE;CAC7B,GAAG,IAAI,KAAK,CAAA;CACZ,GAAG,MAAM,OAAO,GAAG,EAAE,CAAA;CACrB,GAAG,QAAQ,KAAK,GAAG,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG;CAClC,IAAI,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;CACvB,IAAG;CACH,GAAG,OAAO,OAAO,CAAA;CACjB,GAAE;CACF,EAAE,IAAI,WAAW,CAAC,MAAM,EAAE;CAC1B,GAAG,MAAM,OAAO,GAAG,QAAQ,CAAC,WAAW,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;CACvD,GAAG,OAAO,CAAC,OAAO,CAAC,CAAC,KAAK,KAAK;CAC9B,IAAI,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,EAAE;CAC7B,KAAK,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;CAC7D,KAAK,IAAI,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;CACnC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;CAC7E,MAAK;CACL,KAAI;CACJ,IAAI,CAAC,CAAA;CACL,GAAG,MAAM;CACT,GAAG,MAAM,KAAK,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;CACjD,GAAG,IAAI,KAAK,IAAI,KAAK,CAAC,KAAK,IAAI,IAAI,EAAE;CACrC,IAAI,MAAM,WAAW,GAAG,cAAc,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAA;CAC5D,IAAI,IAAI,WAAW,KAAK,KAAK,CAAC,CAAC,CAAC,EAAE;CAClC,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;CAC5E,KAAI;CACJ,IAAG;CACH,GAAE;CACF,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,cAAc,CAAC,MAAM,EAAE,WAAW,EAAE;CACrC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAA;CAC3B,EAAE,MAAM,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;;CAExC,EAAE,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE;CACpB,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,MAAM,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;CAC5D,GAAE;;CAEF,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,OAAO,CAAC,WAAW,EAAE,WAAW,EAAE;CACnC,EAAE,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;CACvC,GAAG,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,CAAA;CACvD,GAAE;;CAEF,EAAE,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,CAAA;CACtD,EAAC;;CAED,CAAC,iBAAiB,CAAC,MAAM,EAAE,WAAW,EAAE;CACxC,EAAE,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAA;CAC3B,EAAE,MAAM,YAAY,GAAG,MAAM,CAAC,MAAM,CAAA;CACpC,EAAE;CACF,GAAG,IAAI,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC;CACvC,GAAG,KAAK,KAAK,CAAC,CAAC;CACf,GAAG,KAAK,GAAG,QAAQ,CAAC,OAAO,CAAC,MAAM,EAAE,KAAK,GAAG,YAAY,CAAA;CACxD,IAAI;CACJ,GAAG,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,YAAY,CAAC,CAAA;CAC/D,GAAG,IAAI,QAAQ,KAAK,WAAW,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,GAAG,YAAY,EAAE,WAAW,CAAC,CAAA;CACzF,GAAE;;CAEF,EAAE,OAAO,IAAI,CAAA;CACb,EAAC;;CAED,CAAC,UAAU,CAAC,WAAW,EAAE,WAAW,EAAE;CACtC,EAAE,IAAI,OAAO,WAAW,KAAK,QAAQ,EAAE;CACvC,GAAG,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW,EAAE,WAAW,CAAC,CAAA;CAC1D,GAAE;;CAEF,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;CAC3B,GAAG,MAAM,IAAI,SAAS;CACtB,IAAI,2EAA2E;CAC/E,IAAI,CAAA;CACJ,GAAE;;CAEF,EAAE,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,EAAE,WAAW,CAAC,CAAA;CACtD,EAAC;CACD;;CC33BA,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC;AACnD;CACe,MAAM,MAAM,CAAC;CAC5B,CAAC,WAAW,CAAC,OAAO,GAAG,EAAE,EAAE;CAC3B,EAAE,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC;CACnC,EAAE,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,KAAK,SAAS,GAAG,OAAO,CAAC,SAAS,GAAG,IAAI,CAAC;CAC9E,EAAE,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;CACpB,EAAE,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;CAC1B,EAAE,IAAI,CAAC,2BAA2B,GAAG,EAAE,CAAC;CACxC,EAAE;AACF;CACA,CAAC,SAAS,CAAC,MAAM,EAAE;CACnB,EAAE,IAAI,MAAM,YAAY,WAAW,EAAE;CACrC,GAAG,OAAO,IAAI,CAAC,SAAS,CAAC;CACzB,IAAI,OAAO,EAAE,MAAM;CACnB,IAAI,QAAQ,EAAE,MAAM,CAAC,QAAQ;CAC7B,IAAI,SAAS,EAAE,IAAI,CAAC,SAAS;CAC7B,IAAI,CAAC,CAAC;CACN,GAAG;AACH;CACA,EAAE,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE;CAC5C,GAAG,MAAM,IAAI,KAAK;CAClB,IAAI,sIAAsI;CAC1I,IAAI,CAAC;CACL,GAAG;AACH;CACA,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,uBAAuB,EAAE,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;CACvF,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,EAAE,MAAM,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;CACjF,GAAG,CAAC,CAAC;AACL;CACA,EAAE,IAAI,MAAM,CAAC,SAAS,KAAK,SAAS,EAAE;CACtC;CACA,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;CACrC,GAAG;AACH;CACA,EAAE,IAAI,MAAM,CAAC,QAAQ,EAAE;CACvB,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,MAAM,CAAC,QAAQ,CAAC,EAAE;CAC5E,IAAI,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;CAClF,IAAI,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,MAAM,CAAC,QAAQ,EAAE,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAAC;CAC7F,IAAI,MAAM;CACV,IAAI,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC;CAC/F,IAAI,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,KAAK,YAAY,CAAC,OAAO,EAAE;CAC1D,KAAK,MAAM,IAAI,KAAK,CAAC,CAAC,+BAA+B,EAAE,MAAM,CAAC,QAAQ,CAAC,qBAAqB,CAAC,CAAC,CAAC;CAC/F,KAAK;CACL,IAAI;CACJ,GAAG;AACH;CACA,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;CAC5B,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;AACF;CACA,CAAC,MAAM,CAAC,GAAG,EAAE,OAAO,EAAE;CACtB,EAAE,IAAI,CAAC,SAAS,CAAC;CACjB,GAAG,OAAO,EAAE,IAAI,WAAW,CAAC,GAAG,CAAC;CAChC,GAAG,SAAS,EAAE,CAAC,OAAO,IAAI,OAAO,CAAC,SAAS,KAAK,EAAE;CAClD,GAAG,CAAC,CAAC;AACL;CACA,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;AACF;CACA,CAAC,KAAK,GAAG;CACT,EAAE,MAAM,MAAM,GAAG,IAAI,MAAM,CAAC;CAC5B,GAAG,KAAK,EAAE,IAAI,CAAC,KAAK;CACpB,GAAG,SAAS,EAAE,IAAI,CAAC,SAAS;CAC5B,GAAG,CAAC,CAAC;AACL;CACA,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;CACnC,GAAG,MAAM,CAAC,SAAS,CAAC;CACpB,IAAI,QAAQ,EAAE,MAAM,CAAC,QAAQ;CAC7B,IAAI,OAAO,EAAE,MAAM,CAAC,OAAO,CAAC,KAAK,EAAE;CACnC,IAAI,SAAS,EAAE,MAAM,CAAC,SAAS;CAC/B,IAAI,CAAC,CAAC;CACN,GAAG,CAAC,CAAC;AACL;CACA,EAAE,OAAO,MAAM,CAAC;CAChB,EAAE;AACF;CACA,CAAC,kBAAkB,CAAC,OAAO,GAAG,EAAE,EAAE;CAClC,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;CACnB,EAAE,IAAI,mBAAmB,GAAG,SAAS,CAAC;CACtC,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;CACnC,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;CAC7D,IAAI,IAAI,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;CAChD,IAAI,CAAC,CAAC;CACN,GAAG,CAAC,CAAC;AACL;CACA,EAAE,MAAM,QAAQ,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC/C;CACA,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE;CAClB,GAAG,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;CAChC,GAAG;AACH;CACA,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK;CACtC,GAAG,IAAI,CAAC,GAAG,CAAC,EAAE;CACd,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;CACrC,IAAI;AACJ;CACA,GAAG,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC,2BAA2B,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;CAChG,GAAG,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC;CACtC,GAAG,MAAM,MAAM,GAAG,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;AACnD;CACA,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE;CAC1B,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;CACxC,IAAI;AACJ;CACA,GAAG,WAAW,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,KAAK,KAAK;CAC9C,IAAI,MAAM,GAAG,GAAG,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AACpC;CACA,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;AAC1D;CACA,IAAI,IAAI,MAAM,CAAC,QAAQ,EAAE;CACzB,KAAK,IAAI,KAAK,CAAC,MAAM,EAAE;CACvB,MAAM,QAAQ,CAAC,OAAO;CACtB,OAAO,WAAW;CAClB,OAAO,KAAK,CAAC,OAAO;CACpB,OAAO,GAAG;CACV,OAAO,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;CAC3D,OAAO,CAAC;CACR,MAAM,MAAM;CACZ,MAAM,QAAQ,CAAC,gBAAgB;CAC/B,OAAO,WAAW;CAClB,OAAO,KAAK;CACZ,OAAO,WAAW,CAAC,QAAQ;CAC3B,OAAO,GAAG;CACV,OAAO,WAAW,CAAC,kBAAkB;CACrC,OAAO,CAAC;CACR,MAAM;CACN,KAAK,MAAM;CACX,KAAK,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;CACrC,KAAK;AACL;CACA,IAAI,IAAI,KAAK,CAAC,KAAK,CAAC,MAAM,EAAE,QAAQ,CAAC,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;CAC1D,IAAI,CAAC,CAAC;AACN;CACA,GAAG,IAAI,WAAW,CAAC,KAAK,EAAE;CAC1B,IAAI,QAAQ,CAAC,OAAO,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;CACxC,IAAI;AACJ;CACA,GAAG,IAAI,MAAM,CAAC,UAAU,IAAI,WAAW,KAAK,CAAC,CAAC,EAAE;CAChD,IAAI,IAAI,mBAAmB,KAAK,SAAS,EAAE;CAC3C,KAAK,mBAAmB,GAAG,EAAE,CAAC;CAC9B,KAAK;CACL,IAAI,mBAAmB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;CAC1C,IAAI;CACJ,GAAG,CAAC,CAAC;AACL;CACA,EAAE,OAAO;CACT,GAAG,IAAI,EAAE,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,GAAG,EAAE,GAAG,SAAS;CACrE,GAAG,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK;CAC/C,IAAI,OAAO,OAAO,CAAC,IAAI,GAAG,eAAe,CAAC,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC;CAC3F,IAAI,CAAC;CACL,GAAG,cAAc,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC,MAAM,KAAK;CACtD,IAAI,OAAO,OAAO,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;CAC1D,IAAI,CAAC;CACL,GAAG,KAAK;CACR,GAAG,QAAQ,EAAE,QAAQ,CAAC,GAAG;CACzB,GAAG,mBAAmB;CACtB,GAAG,CAAC;CACJ,EAAE;AACF;CACA,CAAC,WAAW,CAAC,OAAO,EAAE;CACtB,EAAE,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC;CACzD,EAAE;AACF;CACA,CAAC,eAAe,GAAG;CACnB,EAAE,MAAM,kBAAkB,GAAG,EAAE,CAAC;AAChC;CACA,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAK;CACnC,GAAG,MAAM,SAAS,GAAG,MAAM,CAAC,OAAO,CAAC,mBAAmB,EAAE,CAAC;AAC1D;CACA,GAAG,IAAI,SAAS,KAAK,IAAI,EAAE,OAAO;AAClC;CACA,GAAG,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,EAAE,kBAAkB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC;CACzE,GAAG,kBAAkB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;CACtC,GAAG,CAAC,CAAC;AACL;CACA,EAAE;CACF,GAAG,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK;CAClD,IAAI,OAAO,kBAAkB,CAAC,CAAC,CAAC,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;CACzD,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI;CAChB,IAAI;CACJ,EAAE;AACF;CACA,CAAC,MAAM,CAAC,SAAS,EAAE;CACnB,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE;CACzB,GAAG,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;CACtC,GAAG;AACH;CACA,EAAE,IAAI,SAAS,KAAK,EAAE,EAAE,OAAO,IAAI,CAAC;AACpC;CACA,EAAE,IAAI,eAAe,GAAG,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC;AACrE;CACA,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK;CACtC,GAAG,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;CACxF,GAAG,MAAM,WAAW,GAAG,eAAe,KAAK,CAAC,GAAG,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AAC9E;CACA,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,SAAS,EAAE;CACpC,IAAI,OAAO,EAAE,MAAM,CAAC,qBAAqB;CACzC,IAAI,WAAW;CACf,IAAI,CAAC,CAAC;AACN;CACA,GAAG,eAAe,GAAG,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC;CACxD,GAAG,CAAC,CAAC;AACL;CACA,EAAE,IAAI,IAAI,CAAC,KAAK,EAAE;CAClB,GAAG,IAAI,CAAC,KAAK;CACb,IAAI,SAAS;CACb,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,UAAU,EAAE,CAAC,KAAK,EAAE,KAAK,KAAK;CACrD,KAAK,OAAO,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG,KAAK,GAAG,KAAK,CAAC;CAClD,KAAK,CAAC,CAAC;CACP,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;AACF;CACA,CAAC,OAAO,CAAC,GAAG,EAAE;CACd,EAAE,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,IAAI,CAAC,KAAK,CAAC;CAChC,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;AACF;CACA,CAAC,QAAQ,GAAG;CACZ,EAAE,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO;CAC3B,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,CAAC,KAAK;CACvB,IAAI,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,KAAK,SAAS,GAAG,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;CACzF,IAAI,MAAM,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,SAAS,GAAG,EAAE,IAAI,MAAM,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;AACrE;CACA,IAAI,OAAO,GAAG,CAAC;CACf,IAAI,CAAC;CACL,IAAI,IAAI,CAAC,EAAE,CAAC,CAAC;AACb;CACA,EAAE,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;CAC3B,EAAE;AACF;CACA,CAAC,OAAO,GAAG;CACX,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,OAAO,KAAK,CAAC;CAC3D,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,EAAE,OAAO,KAAK,CAAC;CAC7E,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;AACF;CACA,CAAC,MAAM,GAAG;CACV,EAAE,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM;CAC5B,GAAG,CAAC,MAAM,EAAE,MAAM,KAAK,MAAM,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,EAAE;CACvD,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM;CACpB,GAAG,CAAC;CACJ,EAAE;AACF;CACA,CAAC,SAAS,GAAG;CACb,EAAE,OAAO,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;CAC/B,EAAE;AACF;CACA,CAAC,IAAI,CAAC,QAAQ,EAAE;CAChB,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;CACpD,EAAE;AACF;CACA,CAAC,SAAS,CAAC,QAAQ,EAAE;CACrB,EAAE,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,GAAG,IAAI,QAAQ,IAAI,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC;CACzD,EAAE,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;AAC1C;CACA,EAAE,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;CACnB,GAAG,IAAI,MAAM,CAAC;CACd,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC;AACb;CACA,GAAG,GAAG;CACN,IAAI,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;CAC/B,IAAI,IAAI,CAAC,MAAM,EAAE;CACjB,KAAK,MAAM;CACX,KAAK;CACL,IAAI,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,gBAAgB,CAAC,QAAQ,CAAC,EAAE;CACxD,GAAG;AACH;CACA,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;AACF;CACA,CAAC,OAAO,CAAC,QAAQ,EAAE;CACnB,EAAE,MAAM,EAAE,GAAG,IAAI,MAAM,CAAC,CAAC,QAAQ,IAAI,KAAK,IAAI,IAAI,CAAC,CAAC;AACpD;CACA,EAAE,IAAI,MAAM,CAAC;CACb,EAAE,IAAI,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC;AAClC;CACA,EAAE,GAAG;CACL,GAAG,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC;CAC9B,GAAG,IAAI,CAAC,MAAM,EAAE;CAChB,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC;CAC5C,IAAI,MAAM;CACV,IAAI;CACJ,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;AACrD;CACA,EAAE,OAAO,IAAI,CAAC;CACd,EAAE;CACF;;CCpSA,WAAW,CAAC,MAAM,GAAG,MAAM,CAAC;CAC5B,WAAW,CAAC,SAAS,GAAG,SAAS,CAAC;CAClC,WAAW,CAAC,OAAO,GAAG,WAAW,CAAC;;;;;;;;", "x_google_ignoreList": [2]}