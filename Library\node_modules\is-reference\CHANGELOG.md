# is-reference changelog

## 1.2.1

* Relax version range for `@types/estree`

## 1.2.0

* Handle class fields ([#](https://github.com/<PERSON>-<PERSON>/is-reference/pull/8))

## 1.1.4

* Disregarded imported specifiers if they differ from local specifiers

## 1.1.3

* Handle expressions without a Program

## 1.1.2

* Ignore labels in break/continue statements ([#4](https://github.com/<PERSON>-<PERSON>/is-reference/pull/4))

## 1.1.1

* Prevent false positives with labeled statements

## 1.1.0

* Rewrite in TypeScript, add declarations

## 1.0.1

* Ensure `isReference` returns a boolean

## 1.0.0

* First release
