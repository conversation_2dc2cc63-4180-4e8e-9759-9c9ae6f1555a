{"version": 3, "file": "index.cjs", "sources": ["../src/constants.ts", "../src/utils/check.ts", "../src/utils/array.ts", "../src/utils/clone.ts", "../src/utils/object.ts", "../src/utils/options.ts", "../src/module.ts", "../src/presets.ts"], "sourcesContent": ["export enum PriorityName {\n    LEFT = 'left',\n    RIGHT = 'right',\n}\n", "export function isObject(item: unknown) : item is Record<string, any> {\n    return (\n        !!item &&\n        typeof item === 'object' &&\n        !Array.isArray(item)\n    );\n}\n\nexport function isSafeKey(key: string) : boolean {\n    return key !== '__proto__' &&\n        key !== 'prototype' &&\n        key !== 'constructor';\n}\n\nexport function isEqual(x: any, y: any): boolean {\n    if (Object.is(x, y)) return true;\n\n    if (x instanceof Date && y instanceof Date) {\n        return x.getTime() === y.getTime();\n    }\n\n    if (x instanceof RegExp && y instanceof RegExp) {\n        return x.toString() === y.toString();\n    }\n\n    if (\n        isObject(x) &&\n        isObject(y)\n    ) {\n        const keysX = Reflect.ownKeys(x) as string[];\n        const keysY = Reflect.ownKeys(y) as string[];\n        if (keysX.length !== keysY.length) {\n            return false;\n        }\n\n        for (let i = 0; i < keysX.length; i++) {\n            const key = keysX[i];\n            if (!Reflect.has(y, key) || !isEqual(x[key], y[key])) {\n                return false;\n            }\n        }\n\n        return true;\n    }\n\n    if (\n        Array.isArray(x) &&\n        Array.isArray(y)\n    ) {\n        if (x.length !== y.length) {\n            return false;\n        }\n\n        for (let i = 0; i < x.length; i++) {\n            if (!isEqual(x[i], y[i])) {\n                return false;\n            }\n        }\n\n        return true;\n    }\n\n    return false;\n}\n", "import { isEqual } from './check';\n\nexport function distinctArray<T = any>(arr: T[]) : T[] {\n    for (let i = 0; i < arr.length; i++) {\n        for (let j = arr.length - 1; j > i; j--) {\n            if (isEqual(arr[i], arr[j])) {\n                arr.splice(j, 1);\n            }\n        }\n    }\n\n    return arr;\n}\n", "import { isObject } from './check';\n\n/* istanbul ignore next */\nconst gT = (() => {\n    if (typeof globalThis !== 'undefined') {\n        return globalThis;\n    }\n\n    // eslint-disable-next-line no-restricted-globals\n    if (typeof self !== 'undefined') {\n        // eslint-disable-next-line no-restricted-globals\n        return self;\n    }\n\n    if (typeof window !== 'undefined') {\n        return window;\n    }\n\n    if (typeof global !== 'undefined') {\n        return global;\n    }\n\n    throw new Error('unable to locate global object');\n})();\n\nexport function polyfillClone<T>(input: T) {\n    const map = new WeakMap();\n\n    const fn = <A>(value: A) : A => {\n        if (Array.isArray(value)) {\n            if (map.has(value)) {\n                return map.get(value);\n            }\n\n            const cloned = [] as A;\n            map.set(value, cloned);\n\n            value.map((el) => (cloned as any[]).push(fn(el)));\n\n            return cloned;\n        }\n\n        if (isObject(value)) {\n            if (map.has(value)) {\n                return map.get(value);\n            }\n\n            const output = {} as A;\n            const keys = Object.keys(value);\n\n            map.set(value, output);\n            for (let i = 0; i < keys.length; i++) {\n                output[keys[i] as keyof A] = fn(value[keys[i]]);\n            }\n\n            return output;\n        }\n\n        return value;\n    };\n\n    return fn(input);\n}\n\n/* istanbul ignore next */\nexport function clone<T>(value: T) : T {\n    if (gT.structuredClone) {\n        return gT.structuredClone(value);\n    }\n\n    /* istanbul ignore next */\n    return polyfillClone(value);\n}\n", "// eslint-disable-next-line @typescript-eslint/ban-types\nexport function hasOwnProperty<X extends {}, Y extends PropertyKey>(obj: X, prop: Y): obj is X & Record<Y, unknown> {\n    return Object.prototype.hasOwnProperty.call(obj, prop);\n}\n", "import { PriorityName } from '../constants';\nimport type { Options, OptionsInput } from '../type';\n\nexport function buildOptions(options: OptionsInput = {}) : Options {\n    options.array = options.array ?? true;\n    options.arrayDistinct = options.arrayDistinct ?? false;\n    options.clone = options.clone ?? false;\n    options.inPlace = options.inPlace ?? false;\n    options.priority = options.priority || PriorityName.LEFT;\n    options.arrayPriority = options.arrayPriority || options.priority;\n\n    return options as Options;\n}\n\nexport function togglePriority(priority: `${PriorityName}`) {\n    return priority === PriorityName.LEFT ?\n        `${PriorityName.RIGHT}` :\n        `${PriorityName.LEFT}`;\n}\n", "import { PriorityName } from './constants';\nimport type {\n    Merger, MergerContext,\n    MergerResult,\n    MergerSource,\n    MergerSourceUnwrap,\n    OptionsInput,\n} from './type';\n\nimport {\n    buildOptions,\n    clone,\n    distinctArray,\n    hasOwnProperty,\n    isObject,\n    isSafeKey, togglePriority,\n} from './utils';\n\nfunction baseMerger<B extends MergerSource[]>(\n    context: MergerContext,\n    ...sources: B\n) : MergerResult<B> {\n    let target : MergerSourceUnwrap<B>;\n    let source : MergerSourceUnwrap<B> | undefined;\n\n    let { priority } = context.options;\n    if (sources.length >= 2) {\n        if (\n            Array.isArray(sources.at(0)) &&\n            Array.isArray(sources.at(-1))\n        ) {\n            priority = context.options.arrayPriority;\n        }\n    }\n\n    if (priority === PriorityName.RIGHT) {\n        target = sources.pop() as MergerSourceUnwrap<B>;\n        source = sources.pop() as MergerSourceUnwrap<B>;\n    } else {\n        target = sources.shift() as MergerSourceUnwrap<B>;\n        source = sources.shift() as MergerSourceUnwrap<B>;\n    }\n\n    if (!source) {\n        if (\n            Array.isArray(target) &&\n            context.options.arrayDistinct\n        ) {\n            return distinctArray(target) as MergerResult<B>;\n        }\n\n        return target as MergerResult<B>;\n    }\n\n    if (\n        Array.isArray(target) &&\n        Array.isArray(source)\n    ) {\n        target.push(...source as MergerSource[]);\n\n        if (context.options.arrayPriority === PriorityName.RIGHT) {\n            return baseMerger(\n                context,\n                ...sources,\n                target,\n            ) as MergerResult<B>;\n        }\n\n        return baseMerger(\n            context,\n            target,\n            ...sources,\n        ) as MergerResult<B>;\n    }\n\n    context.map.set(source, true);\n\n    if (\n        isObject(target) &&\n        isObject(source)\n    ) {\n        const keys = Object.keys(source);\n        for (let i = 0; i < keys.length; i++) {\n            const key = keys[i] as (keyof MergerSourceUnwrap<B>);\n\n            if (hasOwnProperty(target, key)) {\n                if (!isSafeKey(key as string)) {\n                    continue;\n                }\n\n                if (context.options.strategy) {\n                    const applied = context.options.strategy(target, key as string, source[key]);\n                    if (typeof applied !== 'undefined') {\n                        continue;\n                    }\n                }\n\n                if (\n                    isObject(target[key]) &&\n                    isObject(source[key])\n                ) {\n                    if (context.map.has(source[key])) {\n                        const sourceKeys = Object.keys(source[key] as Record<string, any>);\n                        for (let j = 0; j < sourceKeys.length; j++) {\n                            if (\n                                isSafeKey(sourceKeys[j]) &&\n                                !hasOwnProperty(target[key] as Record<string, any>, sourceKeys[j])\n                            ) {\n                                (target[key] as Record<string, any>)[sourceKeys[j]] = (source[key] as Record<string, any>)[sourceKeys[j]];\n                            }\n                        }\n\n                        continue;\n                    }\n\n                    if (context.options.priority === PriorityName.RIGHT) {\n                        target[key] = baseMerger(\n                            context,\n                            source[key] as MergerSource,\n                            target[key] as MergerSource,\n                        ) as MergerSourceUnwrap<B>[keyof MergerSourceUnwrap<B>];\n                    } else {\n                        target[key] = baseMerger(\n                            context,\n                            target[key] as MergerSource,\n                            source[key] as MergerSource,\n                        ) as MergerSourceUnwrap<B>[keyof MergerSourceUnwrap<B>];\n                    }\n\n                    continue;\n                }\n\n                if (\n                    context.options.array &&\n                    Array.isArray(target[key]) &&\n                    Array.isArray(source[key])\n                ) {\n                    const arrayPriority = context.options.priority !== context.options.arrayPriority ?\n                        togglePriority(context.options.arrayPriority) :\n                        context.options.arrayPriority;\n\n                    switch (arrayPriority) {\n                        case PriorityName.LEFT:\n                            Object.assign(target, {\n                                [key]: baseMerger(context, target[key] as MergerSource, source[key] as MergerSource),\n                            });\n                            break;\n                        case PriorityName.RIGHT:\n                            Object.assign(target, {\n                                [key]: baseMerger(context, source[key] as MergerSource, target[key] as MergerSource),\n                            });\n                            break;\n                    }\n                }\n            } else {\n                Object.assign(target, {\n                    [key]: source[key],\n                });\n            }\n        }\n    }\n\n    context.map = new WeakMap();\n\n    if (context.options.priority === PriorityName.RIGHT) {\n        return baseMerger(context, ...sources, target) as MergerResult<B>;\n    }\n\n    return baseMerger(context, target, ...sources) as MergerResult<B>;\n}\n\nexport function createMerger(input?: OptionsInput) : Merger {\n    const options = buildOptions(input);\n\n    return <B extends MergerSource[]>(\n        ...sources: B\n    ) : MergerResult<B> => {\n        if (!sources.length) {\n            throw new SyntaxError('At least one input element is required.');\n        }\n\n        const ctx : MergerContext = {\n            options,\n            map: new WeakMap<any, any>(),\n        };\n\n        if (options.clone) {\n            return baseMerger(ctx, ...clone(sources));\n        }\n\n        if (!options.inPlace) {\n            if (\n                Array.isArray(sources.at(0)) &&\n                options.arrayPriority === PriorityName.LEFT\n            ) {\n                sources.unshift([]);\n                return baseMerger(ctx, ...sources);\n            }\n\n            if (\n                Array.isArray(sources.at(-1)) &&\n                options.arrayPriority === PriorityName.RIGHT\n            ) {\n                sources.push([]);\n                return baseMerger(ctx, ...sources);\n            }\n\n            if (options.priority === PriorityName.LEFT) {\n                sources.unshift({});\n            } else {\n                sources.push({});\n            }\n        }\n\n        return baseMerger(ctx, ...sources);\n    };\n}\n\nexport const merge = createMerger();\n", "import { createMerger } from './module';\nimport type { MergerResult } from './type';\n\n/**\n * Assign source attributes to a target object.\n *\n * @param target\n * @param sources\n */\nexport function assign<A extends Record<string, any>, B extends Record<string, any>[]>(\n    target: A,\n    ...sources: B\n) : A & MergerResult<B> {\n    return createMerger({\n        inPlace: true,\n        priority: 'left',\n        array: false,\n    })(target, ...sources) as A & MergerResult<B>;\n}\n"], "names": ["PriorityName", "isObject", "item", "Array", "isArray", "isSafeKey", "key", "isEqual", "x", "y", "Object", "is", "Date", "getTime", "RegExp", "toString", "keysX", "Reflect", "ownKeys", "keysY", "length", "i", "has", "distinctArray", "arr", "j", "splice", "gT", "globalThis", "self", "window", "global", "Error", "polyfillClone", "input", "map", "WeakMap", "fn", "value", "get", "cloned", "set", "el", "push", "output", "keys", "clone", "structuredClone", "hasOwnProperty", "obj", "prop", "prototype", "call", "buildOptions", "options", "array", "arrayDistinct", "inPlace", "priority", "LEFT", "arrayPriority", "togglePriority", "RIGHT", "baseMerger", "context", "sources", "target", "source", "at", "pop", "shift", "strategy", "applied", "sourceKeys", "assign", "createMerger", "SyntaxError", "ctx", "unshift", "merge"], "mappings": ";;;AAAYA,CAAAA,SAAAA,YAAAA,EAAAA;;;GAAAA,oBAAAA,KAAAA,oBAAAA,GAAAA,EAAAA,CAAAA,CAAAA;;ACAL,SAASC,SAASC,IAAa,EAAA;IAClC,OACI,CAAC,CAACA,IACF,IAAA,OAAOA,SAAS,QAChB,IAAA,CAACC,KAAMC,CAAAA,OAAO,CAACF,IAAAA,CAAAA,CAAAA;AAEvB,CAAA;AAEO,SAASG,UAAUC,GAAW,EAAA;AACjC,IAAA,OAAOA,GAAQ,KAAA,WAAA,IACXA,GAAQ,KAAA,WAAA,IACRA,GAAQ,KAAA,aAAA,CAAA;AAChB,CAAA;AAEO,SAASC,OAAAA,CAAQC,CAAM,EAAEC,CAAM,EAAA;AAClC,IAAA,IAAIC,MAAOC,CAAAA,EAAE,CAACH,CAAAA,EAAGC,IAAI,OAAO,IAAA,CAAA;IAE5B,IAAID,CAAAA,YAAaI,IAAQH,IAAAA,CAAAA,YAAaG,IAAM,EAAA;AACxC,QAAA,OAAOJ,CAAEK,CAAAA,OAAO,EAAOJ,KAAAA,CAAAA,CAAEI,OAAO,EAAA,CAAA;AACpC,KAAA;IAEA,IAAIL,CAAAA,YAAaM,MAAUL,IAAAA,CAAAA,YAAaK,MAAQ,EAAA;AAC5C,QAAA,OAAON,CAAEO,CAAAA,QAAQ,EAAON,KAAAA,CAAAA,CAAEM,QAAQ,EAAA,CAAA;AACtC,KAAA;IAEA,IACId,QAAAA,CAASO,CACTP,CAAAA,IAAAA,QAAAA,CAASQ,CACX,CAAA,EAAA;QACE,MAAMO,KAAAA,GAAQC,OAAQC,CAAAA,OAAO,CAACV,CAAAA,CAAAA,CAAAA;QAC9B,MAAMW,KAAAA,GAAQF,OAAQC,CAAAA,OAAO,CAACT,CAAAA,CAAAA,CAAAA;AAC9B,QAAA,IAAIO,KAAMI,CAAAA,MAAM,KAAKD,KAAAA,CAAMC,MAAM,EAAE;YAC/B,OAAO,KAAA,CAAA;AACX,SAAA;AAEA,QAAA,IAAK,IAAIC,CAAI,GAAA,CAAA,EAAGA,IAAIL,KAAMI,CAAAA,MAAM,EAAEC,CAAK,EAAA,CAAA;YACnC,MAAMf,GAAAA,GAAMU,KAAK,CAACK,CAAE,CAAA,CAAA;AACpB,YAAA,IAAI,CAACJ,OAAAA,CAAQK,GAAG,CAACb,GAAGH,GAAQ,CAAA,IAAA,CAACC,OAAQC,CAAAA,CAAC,CAACF,GAAI,CAAA,EAAEG,CAAC,CAACH,IAAI,CAAG,EAAA;gBAClD,OAAO,KAAA,CAAA;AACX,aAAA;AACJ,SAAA;QAEA,OAAO,IAAA,CAAA;AACX,KAAA;AAEA,IAAA,IACIH,MAAMC,OAAO,CAACI,MACdL,KAAMC,CAAAA,OAAO,CAACK,CAChB,CAAA,EAAA;AACE,QAAA,IAAID,CAAEY,CAAAA,MAAM,KAAKX,CAAAA,CAAEW,MAAM,EAAE;YACvB,OAAO,KAAA,CAAA;AACX,SAAA;AAEA,QAAA,IAAK,IAAIC,CAAI,GAAA,CAAA,EAAGA,IAAIb,CAAEY,CAAAA,MAAM,EAAEC,CAAK,EAAA,CAAA;YAC/B,IAAI,CAACd,QAAQC,CAAC,CAACa,EAAE,EAAEZ,CAAC,CAACY,CAAAA,CAAE,CAAG,EAAA;gBACtB,OAAO,KAAA,CAAA;AACX,aAAA;AACJ,SAAA;QAEA,OAAO,IAAA,CAAA;AACX,KAAA;IAEA,OAAO,KAAA,CAAA;AACX;;AC7DO,SAASE,cAAuBC,GAAQ,EAAA;AAC3C,IAAA,IAAK,IAAIH,CAAI,GAAA,CAAA,EAAGA,IAAIG,GAAIJ,CAAAA,MAAM,EAAEC,CAAK,EAAA,CAAA;QACjC,IAAK,IAAII,IAAID,GAAIJ,CAAAA,MAAM,GAAG,CAAGK,EAAAA,CAAAA,GAAIJ,GAAGI,CAAK,EAAA,CAAA;YACrC,IAAIlB,OAAAA,CAAQiB,GAAG,CAACH,CAAAA,CAAE,EAAEG,GAAG,CAACC,EAAE,CAAG,EAAA;gBACzBD,GAAIE,CAAAA,MAAM,CAACD,CAAG,EAAA,CAAA,CAAA,CAAA;AAClB,aAAA;AACJ,SAAA;AACJ,KAAA;IAEA,OAAOD,GAAAA,CAAAA;AACX;;ACVA,2BACA,MAAMG,EAAK,GAAC,CAAA,IAAA;IACR,IAAI,OAAOC,eAAe,WAAa,EAAA;QACnC,OAAOA,UAAAA,CAAAA;AACX,KAAA;;IAGA,IAAI,OAAOC,SAAS,WAAa,EAAA;;QAE7B,OAAOA,IAAAA,CAAAA;AACX,KAAA;IAEA,IAAI,OAAOC,WAAW,WAAa,EAAA;QAC/B,OAAOA,MAAAA,CAAAA;AACX,KAAA;IAEA,IAAI,OAAOC,WAAW,WAAa,EAAA;QAC/B,OAAOA,MAAAA,CAAAA;AACX,KAAA;AAEA,IAAA,MAAM,IAAIC,KAAM,CAAA,gCAAA,CAAA,CAAA;AACpB,CAAA,GAAA,CAAA;AAEO,SAASC,cAAiBC,KAAQ,EAAA;AACrC,IAAA,MAAMC,MAAM,IAAIC,OAAAA,EAAAA,CAAAA;AAEhB,IAAA,MAAMC,KAAK,CAAIC,KAAAA,GAAAA;QACX,IAAInC,KAAAA,CAAMC,OAAO,CAACkC,KAAQ,CAAA,EAAA;YACtB,IAAIH,GAAAA,CAAIb,GAAG,CAACgB,KAAQ,CAAA,EAAA;gBAChB,OAAOH,GAAAA,CAAII,GAAG,CAACD,KAAAA,CAAAA,CAAAA;AACnB,aAAA;AAEA,YAAA,MAAME,SAAS,EAAE,CAAA;YACjBL,GAAIM,CAAAA,GAAG,CAACH,KAAOE,EAAAA,MAAAA,CAAAA,CAAAA;YAEfF,KAAMH,CAAAA,GAAG,CAAC,CAACO,EAAAA,GAAO,MAAkBC,CAAAA,IAAI,CAACN,EAAGK,CAAAA,EAAAA,CAAAA,CAAAA,CAAAA,CAAAA;YAE5C,OAAOF,MAAAA,CAAAA;AACX,SAAA;AAEA,QAAA,IAAIvC,SAASqC,KAAQ,CAAA,EAAA;YACjB,IAAIH,GAAAA,CAAIb,GAAG,CAACgB,KAAQ,CAAA,EAAA;gBAChB,OAAOH,GAAAA,CAAII,GAAG,CAACD,KAAAA,CAAAA,CAAAA;AACnB,aAAA;AAEA,YAAA,MAAMM,SAAS,EAAC,CAAA;YAChB,MAAMC,IAAAA,GAAOnC,MAAOmC,CAAAA,IAAI,CAACP,KAAAA,CAAAA,CAAAA;YAEzBH,GAAIM,CAAAA,GAAG,CAACH,KAAOM,EAAAA,MAAAA,CAAAA,CAAAA;AACf,YAAA,IAAK,IAAIvB,CAAI,GAAA,CAAA,EAAGA,IAAIwB,IAAKzB,CAAAA,MAAM,EAAEC,CAAK,EAAA,CAAA;AAClCuB,gBAAAA,MAAM,CAACC,IAAI,CAACxB,CAAAA,CAAE,CAAY,GAAGgB,EAAGC,CAAAA,KAAK,CAACO,IAAI,CAACxB,CAAAA,CAAE,CAAC,CAAA,CAAA;AAClD,aAAA;YAEA,OAAOuB,MAAAA,CAAAA;AACX,SAAA;QAEA,OAAON,KAAAA,CAAAA;AACX,KAAA,CAAA;AAEA,IAAA,OAAOD,EAAGH,CAAAA,KAAAA,CAAAA,CAAAA;AACd,CAAA;AAEA,2BACO,SAASY,KAAAA,CAASR,KAAQ,EAAA;IAC7B,IAAIX,EAAAA,CAAGoB,eAAe,EAAE;QACpB,OAAOpB,EAAAA,CAAGoB,eAAe,CAACT,KAAAA,CAAAA,CAAAA;AAC9B,KAAA;+BAGA,OAAOL,aAAcK,CAAAA,KAAAA,CAAAA,CAAAA;AACzB;;ACxEA;AACO,SAASU,cAAAA,CAAoDC,GAAM,EAAEC,IAAO,EAAA;AAC/E,IAAA,OAAOxC,OAAOyC,SAAS,CAACH,cAAc,CAACI,IAAI,CAACH,GAAKC,EAAAA,IAAAA,CAAAA,CAAAA;AACrD;;ACAO,SAASG,YAAAA,CAAaC,OAAwB,GAAA,EAAE,EAAA;AACnCA,IAAAA,IAAAA,cAAAA,CAAAA;AAAhBA,IAAAA,OAAAA,CAAQC,KAAK,GAAGD,CAAAA,iBAAAA,OAAQC,CAAAA,KAAK,YAAbD,cAAiB,GAAA,IAAA,CAAA;AACTA,IAAAA,IAAAA,sBAAAA,CAAAA;AAAxBA,IAAAA,OAAAA,CAAQE,aAAa,GAAGF,CAAAA,yBAAAA,OAAQE,CAAAA,aAAa,YAArBF,sBAAyB,GAAA,KAAA,CAAA;AACjCA,IAAAA,IAAAA,cAAAA,CAAAA;AAAhBA,IAAAA,OAAAA,CAAQR,KAAK,GAAGQ,CAAAA,iBAAAA,OAAQR,CAAAA,KAAK,YAAbQ,cAAiB,GAAA,KAAA,CAAA;AACfA,IAAAA,IAAAA,gBAAAA,CAAAA;AAAlBA,IAAAA,OAAAA,CAAQG,OAAO,GAAGH,CAAAA,mBAAAA,OAAQG,CAAAA,OAAO,YAAfH,gBAAmB,GAAA,KAAA,CAAA;AACrCA,IAAAA,OAAAA,CAAQI,QAAQ,GAAGJ,OAAAA,CAAQI,QAAQ,IAAI1D,qBAAa2D,IAAI,CAAA;AACxDL,IAAAA,OAAAA,CAAQM,aAAa,GAAGN,OAAAA,CAAQM,aAAa,IAAIN,QAAQI,QAAQ,CAAA;IAEjE,OAAOJ,OAAAA,CAAAA;AACX,CAAA;AAEO,SAASO,eAAeH,QAA2B,EAAA;AACtD,IAAA,OAAOA,aAAa1D,oBAAa2D,CAAAA,IAAI,GACjC,CAAC,EAAE3D,oBAAa8D,CAAAA,KAAK,CAAC,CAAC,GACvB,CAAC,EAAE9D,oBAAa2D,CAAAA,IAAI,CAAC,CAAC,CAAA;AAC9B;;ACAA,SAASI,UACLC,CAAAA,OAAsB,EACtB,GAAGC,OAAU,EAAA;IAEb,IAAIC,MAAAA,CAAAA;IACJ,IAAIC,MAAAA,CAAAA;AAEJ,IAAA,IAAI,EAAET,QAAQ,EAAE,GAAGM,QAAQV,OAAO,CAAA;IAClC,IAAIW,OAAAA,CAAQ7C,MAAM,IAAI,CAAG,EAAA;AACrB,QAAA,IACIjB,KAAMC,CAAAA,OAAO,CAAC6D,OAAAA,CAAQG,EAAE,CAAC,CAAA,CAAA,CAAA,IACzBjE,KAAMC,CAAAA,OAAO,CAAC6D,OAAAA,CAAQG,EAAE,CAAC,CAAC,CAC5B,CAAA,CAAA,EAAA;YACEV,QAAWM,GAAAA,OAAAA,CAAQV,OAAO,CAACM,aAAa,CAAA;AAC5C,SAAA;AACJ,KAAA;IAEA,IAAIF,QAAAA,KAAa1D,oBAAa8D,CAAAA,KAAK,EAAE;AACjCI,QAAAA,MAAAA,GAASD,QAAQI,GAAG,EAAA,CAAA;AACpBF,QAAAA,MAAAA,GAASF,QAAQI,GAAG,EAAA,CAAA;KACjB,MAAA;AACHH,QAAAA,MAAAA,GAASD,QAAQK,KAAK,EAAA,CAAA;AACtBH,QAAAA,MAAAA,GAASF,QAAQK,KAAK,EAAA,CAAA;AAC1B,KAAA;AAEA,IAAA,IAAI,CAACH,MAAQ,EAAA;QACT,IACIhE,KAAAA,CAAMC,OAAO,CAAC8D,MAAAA,CAAAA,IACdF,QAAQV,OAAO,CAACE,aAAa,EAC/B;AACE,YAAA,OAAOjC,aAAc2C,CAAAA,MAAAA,CAAAA,CAAAA;AACzB,SAAA;QAEA,OAAOA,MAAAA,CAAAA;AACX,KAAA;AAEA,IAAA,IACI/D,MAAMC,OAAO,CAAC8D,WACd/D,KAAMC,CAAAA,OAAO,CAAC+D,MAChB,CAAA,EAAA;AACED,QAAAA,MAAAA,CAAOvB,IAAI,CAAIwB,GAAAA,MAAAA,CAAAA,CAAAA;AAEf,QAAA,IAAIH,QAAQV,OAAO,CAACM,aAAa,KAAK5D,oBAAAA,CAAa8D,KAAK,EAAE;YACtD,OAAOC,UAAAA,CACHC,YACGC,OACHC,EAAAA,MAAAA,CAAAA,CAAAA;AAER,SAAA;QAEA,OAAOH,UAAAA,CACHC,SACAE,MACGD,EAAAA,GAAAA,OAAAA,CAAAA,CAAAA;AAEX,KAAA;AAEAD,IAAAA,OAAAA,CAAQ7B,GAAG,CAACM,GAAG,CAAC0B,MAAQ,EAAA,IAAA,CAAA,CAAA;IAExB,IACIlE,QAAAA,CAASiE,MACTjE,CAAAA,IAAAA,QAAAA,CAASkE,MACX,CAAA,EAAA;QACE,MAAMtB,IAAAA,GAAOnC,MAAOmC,CAAAA,IAAI,CAACsB,MAAAA,CAAAA,CAAAA;AACzB,QAAA,IAAK,IAAI9C,CAAI,GAAA,CAAA,EAAGA,IAAIwB,IAAKzB,CAAAA,MAAM,EAAEC,CAAK,EAAA,CAAA;YAClC,MAAMf,GAAAA,GAAMuC,IAAI,CAACxB,CAAE,CAAA,CAAA;YAEnB,IAAI2B,cAAAA,CAAekB,QAAQ5D,GAAM,CAAA,EAAA;gBAC7B,IAAI,CAACD,UAAUC,GAAgB,CAAA,EAAA;AAC3B,oBAAA,SAAA;AACJ,iBAAA;AAEA,gBAAA,IAAI0D,OAAQV,CAAAA,OAAO,CAACiB,QAAQ,EAAE;oBAC1B,MAAMC,OAAAA,GAAUR,OAAQV,CAAAA,OAAO,CAACiB,QAAQ,CAACL,MAAQ5D,EAAAA,GAAAA,EAAe6D,MAAM,CAAC7D,GAAI,CAAA,CAAA,CAAA;oBAC3E,IAAI,OAAOkE,YAAY,WAAa,EAAA;AAChC,wBAAA,SAAA;AACJ,qBAAA;AACJ,iBAAA;gBAEA,IACIvE,QAAAA,CAASiE,MAAM,CAAC5D,GAAAA,CAAI,KACpBL,QAASkE,CAAAA,MAAM,CAAC7D,GAAAA,CAAI,CACtB,EAAA;oBACE,IAAI0D,OAAAA,CAAQ7B,GAAG,CAACb,GAAG,CAAC6C,MAAM,CAAC7D,IAAI,CAAG,EAAA;AAC9B,wBAAA,MAAMmE,aAAa/D,MAAOmC,CAAAA,IAAI,CAACsB,MAAM,CAAC7D,GAAI,CAAA,CAAA,CAAA;AAC1C,wBAAA,IAAK,IAAImB,CAAI,GAAA,CAAA,EAAGA,IAAIgD,UAAWrD,CAAAA,MAAM,EAAEK,CAAK,EAAA,CAAA;AACxC,4BAAA,IACIpB,SAAUoE,CAAAA,UAAU,CAAChD,CAAAA,CAAE,KACvB,CAACuB,cAAAA,CAAekB,MAAM,CAAC5D,GAAI,CAAA,EAAyBmE,UAAU,CAAChD,EAAE,CACnE,EAAA;AACGyC,gCAAAA,MAAM,CAAC5D,GAAI,CAAwB,CAACmE,UAAU,CAAChD,EAAE,CAAC,GAAG,MAAO,CAACnB,GAAI,CAAwB,CAACmE,UAAU,CAAChD,EAAE,CAAC,CAAA;AAC7G,6BAAA;AACJ,yBAAA;AAEA,wBAAA,SAAA;AACJ,qBAAA;AAEA,oBAAA,IAAIuC,QAAQV,OAAO,CAACI,QAAQ,KAAK1D,oBAAAA,CAAa8D,KAAK,EAAE;wBACjDI,MAAM,CAAC5D,GAAI,CAAA,GAAGyD,UACVC,CAAAA,OAAAA,EACAG,MAAM,CAAC7D,GAAI,CAAA,EACX4D,MAAM,CAAC5D,GAAI,CAAA,CAAA,CAAA;qBAEZ,MAAA;wBACH4D,MAAM,CAAC5D,GAAI,CAAA,GAAGyD,UACVC,CAAAA,OAAAA,EACAE,MAAM,CAAC5D,GAAI,CAAA,EACX6D,MAAM,CAAC7D,GAAI,CAAA,CAAA,CAAA;AAEnB,qBAAA;AAEA,oBAAA,SAAA;AACJ,iBAAA;AAEA,gBAAA,IACI0D,QAAQV,OAAO,CAACC,KAAK,IACrBpD,KAAAA,CAAMC,OAAO,CAAC8D,MAAM,CAAC5D,GAAAA,CAAI,KACzBH,KAAMC,CAAAA,OAAO,CAAC+D,MAAM,CAAC7D,IAAI,CAC3B,EAAA;oBACE,MAAMsD,aAAAA,GAAgBI,QAAQV,OAAO,CAACI,QAAQ,KAAKM,OAAAA,CAAQV,OAAO,CAACM,aAAa,GAC5EC,cAAeG,CAAAA,OAAAA,CAAQV,OAAO,CAACM,aAAa,IAC5CI,OAAQV,CAAAA,OAAO,CAACM,aAAa,CAAA;oBAEjC,OAAQA,aAAAA;AACJ,wBAAA,KAAK5D,qBAAa2D,IAAI;4BAClBjD,MAAOgE,CAAAA,MAAM,CAACR,MAAQ,EAAA;gCAClB,CAAC5D,GAAAA,GAAMyD,UAAAA,CAAWC,OAASE,EAAAA,MAAM,CAAC5D,GAAI,CAAA,EAAkB6D,MAAM,CAAC7D,GAAI,CAAA,CAAA;AACvE,6BAAA,CAAA,CAAA;AACA,4BAAA,MAAA;AACJ,wBAAA,KAAKN,qBAAa8D,KAAK;4BACnBpD,MAAOgE,CAAAA,MAAM,CAACR,MAAQ,EAAA;gCAClB,CAAC5D,GAAAA,GAAMyD,UAAAA,CAAWC,OAASG,EAAAA,MAAM,CAAC7D,GAAI,CAAA,EAAkB4D,MAAM,CAAC5D,GAAI,CAAA,CAAA;AACvE,6BAAA,CAAA,CAAA;AACA,4BAAA,MAAA;AACR,qBAAA;AACJ,iBAAA;aACG,MAAA;gBACHI,MAAOgE,CAAAA,MAAM,CAACR,MAAQ,EAAA;AAClB,oBAAA,CAAC5D,GAAI,GAAE6D,MAAM,CAAC7D,GAAI,CAAA;AACtB,iBAAA,CAAA,CAAA;AACJ,aAAA;AACJ,SAAA;AACJ,KAAA;IAEA0D,OAAQ7B,CAAAA,GAAG,GAAG,IAAIC,OAAAA,EAAAA,CAAAA;AAElB,IAAA,IAAI4B,QAAQV,OAAO,CAACI,QAAQ,KAAK1D,oBAAAA,CAAa8D,KAAK,EAAE;QACjD,OAAOC,UAAAA,CAAWC,YAAYC,OAASC,EAAAA,MAAAA,CAAAA,CAAAA;AAC3C,KAAA;IAEA,OAAOH,UAAAA,CAAWC,SAASE,MAAWD,EAAAA,GAAAA,OAAAA,CAAAA,CAAAA;AAC1C,CAAA;AAEO,SAASU,aAAazC,KAAoB,EAAA;AAC7C,IAAA,MAAMoB,UAAUD,YAAanB,CAAAA,KAAAA,CAAAA,CAAAA;AAE7B,IAAA,OAAO,CACH,GAAG+B,OAAAA,GAAAA;QAEH,IAAI,CAACA,OAAQ7C,CAAAA,MAAM,EAAE;AACjB,YAAA,MAAM,IAAIwD,WAAY,CAAA,yCAAA,CAAA,CAAA;AAC1B,SAAA;AAEA,QAAA,MAAMC,GAAsB,GAAA;AACxBvB,YAAAA,OAAAA;AACAnB,YAAAA,GAAAA,EAAK,IAAIC,OAAAA,EAAAA;AACb,SAAA,CAAA;QAEA,IAAIkB,OAAAA,CAAQR,KAAK,EAAE;YACf,OAAOiB,UAAAA,CAAWc,QAAQ/B,KAAMmB,CAAAA,OAAAA,CAAAA,CAAAA,CAAAA;AACpC,SAAA;QAEA,IAAI,CAACX,OAAQG,CAAAA,OAAO,EAAE;AAClB,YAAA,IACItD,KAAMC,CAAAA,OAAO,CAAC6D,OAAAA,CAAQG,EAAE,CAAC,CACzBd,CAAAA,CAAAA,IAAAA,OAAAA,CAAQM,aAAa,KAAK5D,oBAAa2D,CAAAA,IAAI,EAC7C;gBACEM,OAAQa,CAAAA,OAAO,CAAC,EAAE,CAAA,CAAA;AAClB,gBAAA,OAAOf,WAAWc,GAAQZ,EAAAA,GAAAA,OAAAA,CAAAA,CAAAA;AAC9B,aAAA;AAEA,YAAA,IACI9D,KAAMC,CAAAA,OAAO,CAAC6D,OAAAA,CAAQG,EAAE,CAAC,CAAC,CAAA,CAAA,CAAA,IAC1Bd,OAAQM,CAAAA,aAAa,KAAK5D,oBAAAA,CAAa8D,KAAK,EAC9C;gBACEG,OAAQtB,CAAAA,IAAI,CAAC,EAAE,CAAA,CAAA;AACf,gBAAA,OAAOoB,WAAWc,GAAQZ,EAAAA,GAAAA,OAAAA,CAAAA,CAAAA;AAC9B,aAAA;AAEA,YAAA,IAAIX,OAAQI,CAAAA,QAAQ,KAAK1D,oBAAAA,CAAa2D,IAAI,EAAE;gBACxCM,OAAQa,CAAAA,OAAO,CAAC,EAAC,CAAA,CAAA;aACd,MAAA;gBACHb,OAAQtB,CAAAA,IAAI,CAAC,EAAC,CAAA,CAAA;AAClB,aAAA;AACJ,SAAA;AAEA,QAAA,OAAOoB,WAAWc,GAAQZ,EAAAA,GAAAA,OAAAA,CAAAA,CAAAA;AAC9B,KAAA,CAAA;AACJ,CAAA;AAEO,MAAMc,QAAQJ,YAAe;;ACvNpC;;;;;AAKC,IACM,SAASD,MAAAA,CACZR,MAAS,EACT,GAAGD,OAAU,EAAA;AAEb,IAAA,OAAOU,YAAa,CAAA;QAChBlB,OAAS,EAAA,IAAA;QACTC,QAAU,EAAA,MAAA;QACVH,KAAO,EAAA,KAAA;AACX,KAAA,CAAA,CAAGW,MAAWD,EAAAA,GAAAA,OAAAA,CAAAA,CAAAA;AAClB;;;;;;;;;;;;;;;"}