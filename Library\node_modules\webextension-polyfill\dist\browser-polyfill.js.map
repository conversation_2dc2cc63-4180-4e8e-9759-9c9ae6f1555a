{"version": 3, "file": "browser-polyfill.js", "names": ["globalThis", "chrome", "runtime", "id", "Error", "browser", "Object", "getPrototypeOf", "prototype", "CHROME_SEND_MESSAGE_CALLBACK_NO_RESPONSE_MESSAGE", "wrapAPIs", "extensionAPIs", "apiMetadata", "keys", "length", "DefaultWeakMap", "WeakMap", "constructor", "createItem", "items", "undefined", "get", "key", "has", "set", "isThenable", "value", "then", "makeCallback", "promise", "metadata", "callback<PERSON><PERSON><PERSON>", "lastError", "reject", "message", "singleCallbackArg", "resolve", "pluralizeArguments", "numArgs", "wrapAsyncFunction", "name", "asyncFunctionWrapper", "target", "args", "min<PERSON><PERSON>s", "maxArgs", "Promise", "fallback<PERSON><PERSON><PERSON><PERSON>Call<PERSON>", "cb<PERSON><PERSON>r", "console", "warn", "noCallback", "wrapMethod", "method", "wrapper", "Proxy", "apply", "targetMethod", "thisObj", "call", "hasOwnProperty", "Function", "bind", "wrapObject", "wrappers", "cache", "create", "handlers", "proxyTarget", "prop", "receiver", "defineProperty", "configurable", "enumerable", "desc", "Reflect", "deleteProperty", "wrapEvent", "wrapperMap", "addListener", "listener", "hasListener", "removeListener", "onRequestFinishedWrappers", "onRequestFinished", "req", "wrappedReq", "get<PERSON>ontent", "onMessageWrappers", "onMessage", "sender", "sendResponse", "didCallSendResponse", "wrappedSendResponse", "sendResponsePromise", "response", "result", "err", "isResultThenable", "sendPromisedResult", "msg", "error", "__mozWebExtensionPolyfillReject__", "catch", "wrappedSendMessageCallback", "reply", "wrappedSendMessage", "apiNamespaceObj", "wrappedCb", "push", "sendMessage", "staticWrappers", "devtools", "network", "onMessageExternal", "tabs", "settingMetadata", "clear", "privacy", "services", "websites", "module", "exports"], "sources": ["browser-polyfill.js"], "sourcesContent": ["/* webextension-polyfill - v0.10.0 - Fri Aug 12 2022 19:42:44 */\n/* -*- Mode: indent-tabs-mode: nil; js-indent-level: 2 -*- */\n/* vim: set sts=2 sw=2 et tw=80: */\n/* This Source Code Form is subject to the terms of the Mozilla Public\n * License, v. 2.0. If a copy of the MPL was not distributed with this\n * file, You can obtain one at http://mozilla.org/MPL/2.0/. */\n\"use strict\";\n\nif (!globalThis.chrome?.runtime?.id) {\n  throw new Error(\"This script should only be loaded in a browser extension.\");\n}\n\nif (typeof globalThis.browser === \"undefined\" || Object.getPrototypeOf(globalThis.browser) !== Object.prototype) {\n  const CHROME_SEND_MESSAGE_CALLBACK_NO_RESPONSE_MESSAGE = \"The message port closed before a response was received.\";\n\n  // Wrapping the bulk of this polyfill in a one-time-use function is a minor\n  // optimization for Firefox. Since Spidermon<PERSON> does not fully parse the\n  // contents of a function until the first time it's called, and since it will\n  // never actually need to be called, this allows the polyfill to be included\n  // in Firefox nearly for free.\n  const wrapAPIs = extensionAPIs => {\n    // NOTE: apiMetadata is associated to the content of the api-metadata.json file\n    // at build time by replacing the following \"include\" with the content of the\n    // JSON file.\n    const apiMetadata = {\n      \"alarms\": {\n        \"clear\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"clearAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"get\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"bookmarks\": {\n        \"create\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getChildren\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getRecent\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getSubTree\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getTree\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"move\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeTree\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"search\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"update\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        }\n      },\n      \"browserAction\": {\n        \"disable\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"enable\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"getBadgeBackgroundColor\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getBadgeText\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getPopup\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getTitle\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"openPopup\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"setBadgeBackgroundColor\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setBadgeText\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setIcon\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"setPopup\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setTitle\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        }\n      },\n      \"browsingData\": {\n        \"remove\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"removeCache\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeCookies\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeDownloads\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeFormData\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeHistory\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeLocalStorage\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removePasswords\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removePluginData\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"settings\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"commands\": {\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"contextMenus\": {\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"update\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        }\n      },\n      \"cookies\": {\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAll\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAllCookieStores\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"set\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"devtools\": {\n        \"inspectedWindow\": {\n          \"eval\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 2,\n            \"singleCallbackArg\": false\n          }\n        },\n        \"panels\": {\n          \"create\": {\n            \"minArgs\": 3,\n            \"maxArgs\": 3,\n            \"singleCallbackArg\": true\n          },\n          \"elements\": {\n            \"createSidebarPane\": {\n              \"minArgs\": 1,\n              \"maxArgs\": 1\n            }\n          }\n        }\n      },\n      \"downloads\": {\n        \"cancel\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"download\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"erase\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getFileIcon\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"open\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"pause\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeFile\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"resume\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"search\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"show\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        }\n      },\n      \"extension\": {\n        \"isAllowedFileSchemeAccess\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"isAllowedIncognitoAccess\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"history\": {\n        \"addUrl\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"deleteAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"deleteRange\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"deleteUrl\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getVisits\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"search\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"i18n\": {\n        \"detectLanguage\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAcceptLanguages\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"identity\": {\n        \"launchWebAuthFlow\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"idle\": {\n        \"queryState\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"management\": {\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"getSelf\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"setEnabled\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"uninstallSelf\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        }\n      },\n      \"notifications\": {\n        \"clear\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"create\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"getPermissionLevel\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"update\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        }\n      },\n      \"pageAction\": {\n        \"getPopup\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getTitle\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"hide\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setIcon\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"setPopup\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"setTitle\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        },\n        \"show\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1,\n          \"fallbackToNoCallback\": true\n        }\n      },\n      \"permissions\": {\n        \"contains\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"request\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"runtime\": {\n        \"getBackgroundPage\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"getPlatformInfo\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"openOptionsPage\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"requestUpdateCheck\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"sendMessage\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 3\n        },\n        \"sendNativeMessage\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"setUninstallURL\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"sessions\": {\n        \"getDevices\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getRecentlyClosed\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"restore\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        }\n      },\n      \"storage\": {\n        \"local\": {\n          \"clear\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"get\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getBytesInUse\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"remove\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"set\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          }\n        },\n        \"managed\": {\n          \"get\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getBytesInUse\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          }\n        },\n        \"sync\": {\n          \"clear\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 0\n          },\n          \"get\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"getBytesInUse\": {\n            \"minArgs\": 0,\n            \"maxArgs\": 1\n          },\n          \"remove\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          },\n          \"set\": {\n            \"minArgs\": 1,\n            \"maxArgs\": 1\n          }\n        }\n      },\n      \"tabs\": {\n        \"captureVisibleTab\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 2\n        },\n        \"create\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"detectLanguage\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"discard\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"duplicate\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"executeScript\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getCurrent\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        },\n        \"getZoom\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getZoomSettings\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"goBack\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"goForward\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"highlight\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"insertCSS\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"move\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        },\n        \"query\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"reload\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 2\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"removeCSS\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"sendMessage\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 3\n        },\n        \"setZoom\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"setZoomSettings\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"update\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        }\n      },\n      \"topSites\": {\n        \"get\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"webNavigation\": {\n        \"getAllFrames\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"getFrame\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        }\n      },\n      \"webRequest\": {\n        \"handlerBehaviorChanged\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 0\n        }\n      },\n      \"windows\": {\n        \"create\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"get\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 2\n        },\n        \"getAll\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getCurrent\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"getLastFocused\": {\n          \"minArgs\": 0,\n          \"maxArgs\": 1\n        },\n        \"remove\": {\n          \"minArgs\": 1,\n          \"maxArgs\": 1\n        },\n        \"update\": {\n          \"minArgs\": 2,\n          \"maxArgs\": 2\n        }\n      }\n    };\n\n    if (Object.keys(apiMetadata).length === 0) {\n      throw new Error(\"api-metadata.json has not been included in browser-polyfill\");\n    }\n\n    /**\n     * A WeakMap subclass which creates and stores a value for any key which does\n     * not exist when accessed, but behaves exactly as an ordinary WeakMap\n     * otherwise.\n     *\n     * @param {function} createItem\n     *        A function which will be called in order to create the value for any\n     *        key which does not exist, the first time it is accessed. The\n     *        function receives, as its only argument, the key being created.\n     */\n    class DefaultWeakMap extends WeakMap {\n      constructor(createItem, items = undefined) {\n        super(items);\n        this.createItem = createItem;\n      }\n\n      get(key) {\n        if (!this.has(key)) {\n          this.set(key, this.createItem(key));\n        }\n\n        return super.get(key);\n      }\n    }\n\n    /**\n     * Returns true if the given object is an object with a `then` method, and can\n     * therefore be assumed to behave as a Promise.\n     *\n     * @param {*} value The value to test.\n     * @returns {boolean} True if the value is thenable.\n     */\n    const isThenable = value => {\n      return value && typeof value === \"object\" && typeof value.then === \"function\";\n    };\n\n    /**\n     * Creates and returns a function which, when called, will resolve or reject\n     * the given promise based on how it is called:\n     *\n     * - If, when called, `chrome.runtime.lastError` contains a non-null object,\n     *   the promise is rejected with that value.\n     * - If the function is called with exactly one argument, the promise is\n     *   resolved to that value.\n     * - Otherwise, the promise is resolved to an array containing all of the\n     *   function's arguments.\n     *\n     * @param {object} promise\n     *        An object containing the resolution and rejection functions of a\n     *        promise.\n     * @param {function} promise.resolve\n     *        The promise's resolution function.\n     * @param {function} promise.reject\n     *        The promise's rejection function.\n     * @param {object} metadata\n     *        Metadata about the wrapped method which has created the callback.\n     * @param {boolean} metadata.singleCallbackArg\n     *        Whether or not the promise is resolved with only the first\n     *        argument of the callback, alternatively an array of all the\n     *        callback arguments is resolved. By default, if the callback\n     *        function is invoked with only a single argument, that will be\n     *        resolved to the promise, while all arguments will be resolved as\n     *        an array if multiple are given.\n     *\n     * @returns {function}\n     *        The generated callback function.\n     */\n    const makeCallback = (promise, metadata) => {\n      return (...callbackArgs) => {\n        if (extensionAPIs.runtime.lastError) {\n          promise.reject(new Error(extensionAPIs.runtime.lastError.message));\n        } else if (metadata.singleCallbackArg ||\n                   (callbackArgs.length <= 1 && metadata.singleCallbackArg !== false)) {\n          promise.resolve(callbackArgs[0]);\n        } else {\n          promise.resolve(callbackArgs);\n        }\n      };\n    };\n\n    const pluralizeArguments = (numArgs) => numArgs == 1 ? \"argument\" : \"arguments\";\n\n    /**\n     * Creates a wrapper function for a method with the given name and metadata.\n     *\n     * @param {string} name\n     *        The name of the method which is being wrapped.\n     * @param {object} metadata\n     *        Metadata about the method being wrapped.\n     * @param {integer} metadata.minArgs\n     *        The minimum number of arguments which must be passed to the\n     *        function. If called with fewer than this number of arguments, the\n     *        wrapper will raise an exception.\n     * @param {integer} metadata.maxArgs\n     *        The maximum number of arguments which may be passed to the\n     *        function. If called with more than this number of arguments, the\n     *        wrapper will raise an exception.\n     * @param {boolean} metadata.singleCallbackArg\n     *        Whether or not the promise is resolved with only the first\n     *        argument of the callback, alternatively an array of all the\n     *        callback arguments is resolved. By default, if the callback\n     *        function is invoked with only a single argument, that will be\n     *        resolved to the promise, while all arguments will be resolved as\n     *        an array if multiple are given.\n     *\n     * @returns {function(object, ...*)}\n     *       The generated wrapper function.\n     */\n    const wrapAsyncFunction = (name, metadata) => {\n      return function asyncFunctionWrapper(target, ...args) {\n        if (args.length < metadata.minArgs) {\n          throw new Error(`Expected at least ${metadata.minArgs} ${pluralizeArguments(metadata.minArgs)} for ${name}(), got ${args.length}`);\n        }\n\n        if (args.length > metadata.maxArgs) {\n          throw new Error(`Expected at most ${metadata.maxArgs} ${pluralizeArguments(metadata.maxArgs)} for ${name}(), got ${args.length}`);\n        }\n\n        return new Promise((resolve, reject) => {\n          if (metadata.fallbackToNoCallback) {\n            // This API method has currently no callback on Chrome, but it return a promise on Firefox,\n            // and so the polyfill will try to call it with a callback first, and it will fallback\n            // to not passing the callback if the first call fails.\n            try {\n              target[name](...args, makeCallback({resolve, reject}, metadata));\n            } catch (cbError) {\n              console.warn(`${name} API method doesn't seem to support the callback parameter, ` +\n                           \"falling back to call it without a callback: \", cbError);\n\n              target[name](...args);\n\n              // Update the API method metadata, so that the next API calls will not try to\n              // use the unsupported callback anymore.\n              metadata.fallbackToNoCallback = false;\n              metadata.noCallback = true;\n\n              resolve();\n            }\n          } else if (metadata.noCallback) {\n            target[name](...args);\n            resolve();\n          } else {\n            target[name](...args, makeCallback({resolve, reject}, metadata));\n          }\n        });\n      };\n    };\n\n    /**\n     * Wraps an existing method of the target object, so that calls to it are\n     * intercepted by the given wrapper function. The wrapper function receives,\n     * as its first argument, the original `target` object, followed by each of\n     * the arguments passed to the original method.\n     *\n     * @param {object} target\n     *        The original target object that the wrapped method belongs to.\n     * @param {function} method\n     *        The method being wrapped. This is used as the target of the Proxy\n     *        object which is created to wrap the method.\n     * @param {function} wrapper\n     *        The wrapper function which is called in place of a direct invocation\n     *        of the wrapped method.\n     *\n     * @returns {Proxy<function>}\n     *        A Proxy object for the given method, which invokes the given wrapper\n     *        method in its place.\n     */\n    const wrapMethod = (target, method, wrapper) => {\n      return new Proxy(method, {\n        apply(targetMethod, thisObj, args) {\n          return wrapper.call(thisObj, target, ...args);\n        },\n      });\n    };\n\n    let hasOwnProperty = Function.call.bind(Object.prototype.hasOwnProperty);\n\n    /**\n     * Wraps an object in a Proxy which intercepts and wraps certain methods\n     * based on the given `wrappers` and `metadata` objects.\n     *\n     * @param {object} target\n     *        The target object to wrap.\n     *\n     * @param {object} [wrappers = {}]\n     *        An object tree containing wrapper functions for special cases. Any\n     *        function present in this object tree is called in place of the\n     *        method in the same location in the `target` object tree. These\n     *        wrapper methods are invoked as described in {@see wrapMethod}.\n     *\n     * @param {object} [metadata = {}]\n     *        An object tree containing metadata used to automatically generate\n     *        Promise-based wrapper functions for asynchronous. Any function in\n     *        the `target` object tree which has a corresponding metadata object\n     *        in the same location in the `metadata` tree is replaced with an\n     *        automatically-generated wrapper function, as described in\n     *        {@see wrapAsyncFunction}\n     *\n     * @returns {Proxy<object>}\n     */\n    const wrapObject = (target, wrappers = {}, metadata = {}) => {\n      let cache = Object.create(null);\n      let handlers = {\n        has(proxyTarget, prop) {\n          return prop in target || prop in cache;\n        },\n\n        get(proxyTarget, prop, receiver) {\n          if (prop in cache) {\n            return cache[prop];\n          }\n\n          if (!(prop in target)) {\n            return undefined;\n          }\n\n          let value = target[prop];\n\n          if (typeof value === \"function\") {\n            // This is a method on the underlying object. Check if we need to do\n            // any wrapping.\n\n            if (typeof wrappers[prop] === \"function\") {\n              // We have a special-case wrapper for this method.\n              value = wrapMethod(target, target[prop], wrappers[prop]);\n            } else if (hasOwnProperty(metadata, prop)) {\n              // This is an async method that we have metadata for. Create a\n              // Promise wrapper for it.\n              let wrapper = wrapAsyncFunction(prop, metadata[prop]);\n              value = wrapMethod(target, target[prop], wrapper);\n            } else {\n              // This is a method that we don't know or care about. Return the\n              // original method, bound to the underlying object.\n              value = value.bind(target);\n            }\n          } else if (typeof value === \"object\" && value !== null &&\n                     (hasOwnProperty(wrappers, prop) ||\n                      hasOwnProperty(metadata, prop))) {\n            // This is an object that we need to do some wrapping for the children\n            // of. Create a sub-object wrapper for it with the appropriate child\n            // metadata.\n            value = wrapObject(value, wrappers[prop], metadata[prop]);\n          } else if (hasOwnProperty(metadata, \"*\")) {\n            // Wrap all properties in * namespace.\n            value = wrapObject(value, wrappers[prop], metadata[\"*\"]);\n          } else {\n            // We don't need to do any wrapping for this property,\n            // so just forward all access to the underlying object.\n            Object.defineProperty(cache, prop, {\n              configurable: true,\n              enumerable: true,\n              get() {\n                return target[prop];\n              },\n              set(value) {\n                target[prop] = value;\n              },\n            });\n\n            return value;\n          }\n\n          cache[prop] = value;\n          return value;\n        },\n\n        set(proxyTarget, prop, value, receiver) {\n          if (prop in cache) {\n            cache[prop] = value;\n          } else {\n            target[prop] = value;\n          }\n          return true;\n        },\n\n        defineProperty(proxyTarget, prop, desc) {\n          return Reflect.defineProperty(cache, prop, desc);\n        },\n\n        deleteProperty(proxyTarget, prop) {\n          return Reflect.deleteProperty(cache, prop);\n        },\n      };\n\n      // Per contract of the Proxy API, the \"get\" proxy handler must return the\n      // original value of the target if that value is declared read-only and\n      // non-configurable. For this reason, we create an object with the\n      // prototype set to `target` instead of using `target` directly.\n      // Otherwise we cannot return a custom object for APIs that\n      // are declared read-only and non-configurable, such as `chrome.devtools`.\n      //\n      // The proxy handlers themselves will still use the original `target`\n      // instead of the `proxyTarget`, so that the methods and properties are\n      // dereferenced via the original targets.\n      let proxyTarget = Object.create(target);\n      return new Proxy(proxyTarget, handlers);\n    };\n\n    /**\n     * Creates a set of wrapper functions for an event object, which handles\n     * wrapping of listener functions that those messages are passed.\n     *\n     * A single wrapper is created for each listener function, and stored in a\n     * map. Subsequent calls to `addListener`, `hasListener`, or `removeListener`\n     * retrieve the original wrapper, so that  attempts to remove a\n     * previously-added listener work as expected.\n     *\n     * @param {DefaultWeakMap<function, function>} wrapperMap\n     *        A DefaultWeakMap object which will create the appropriate wrapper\n     *        for a given listener function when one does not exist, and retrieve\n     *        an existing one when it does.\n     *\n     * @returns {object}\n     */\n    const wrapEvent = wrapperMap => ({\n      addListener(target, listener, ...args) {\n        target.addListener(wrapperMap.get(listener), ...args);\n      },\n\n      hasListener(target, listener) {\n        return target.hasListener(wrapperMap.get(listener));\n      },\n\n      removeListener(target, listener) {\n        target.removeListener(wrapperMap.get(listener));\n      },\n    });\n\n    const onRequestFinishedWrappers = new DefaultWeakMap(listener => {\n      if (typeof listener !== \"function\") {\n        return listener;\n      }\n\n      /**\n       * Wraps an onRequestFinished listener function so that it will return a\n       * `getContent()` property which returns a `Promise` rather than using a\n       * callback API.\n       *\n       * @param {object} req\n       *        The HAR entry object representing the network request.\n       */\n      return function onRequestFinished(req) {\n        const wrappedReq = wrapObject(req, {} /* wrappers */, {\n          getContent: {\n            minArgs: 0,\n            maxArgs: 0,\n          },\n        });\n        listener(wrappedReq);\n      };\n    });\n\n    const onMessageWrappers = new DefaultWeakMap(listener => {\n      if (typeof listener !== \"function\") {\n        return listener;\n      }\n\n      /**\n       * Wraps a message listener function so that it may send responses based on\n       * its return value, rather than by returning a sentinel value and calling a\n       * callback. If the listener function returns a Promise, the response is\n       * sent when the promise either resolves or rejects.\n       *\n       * @param {*} message\n       *        The message sent by the other end of the channel.\n       * @param {object} sender\n       *        Details about the sender of the message.\n       * @param {function(*)} sendResponse\n       *        A callback which, when called with an arbitrary argument, sends\n       *        that value as a response.\n       * @returns {boolean}\n       *        True if the wrapped listener returned a Promise, which will later\n       *        yield a response. False otherwise.\n       */\n      return function onMessage(message, sender, sendResponse) {\n        let didCallSendResponse = false;\n\n        let wrappedSendResponse;\n        let sendResponsePromise = new Promise(resolve => {\n          wrappedSendResponse = function(response) {\n            didCallSendResponse = true;\n            resolve(response);\n          };\n        });\n\n        let result;\n        try {\n          result = listener(message, sender, wrappedSendResponse);\n        } catch (err) {\n          result = Promise.reject(err);\n        }\n\n        const isResultThenable = result !== true && isThenable(result);\n\n        // If the listener didn't returned true or a Promise, or called\n        // wrappedSendResponse synchronously, we can exit earlier\n        // because there will be no response sent from this listener.\n        if (result !== true && !isResultThenable && !didCallSendResponse) {\n          return false;\n        }\n\n        // A small helper to send the message if the promise resolves\n        // and an error if the promise rejects (a wrapped sendMessage has\n        // to translate the message into a resolved promise or a rejected\n        // promise).\n        const sendPromisedResult = (promise) => {\n          promise.then(msg => {\n            // send the message value.\n            sendResponse(msg);\n          }, error => {\n            // Send a JSON representation of the error if the rejected value\n            // is an instance of error, or the object itself otherwise.\n            let message;\n            if (error && (error instanceof Error ||\n                typeof error.message === \"string\")) {\n              message = error.message;\n            } else {\n              message = \"An unexpected error occurred\";\n            }\n\n            sendResponse({\n              __mozWebExtensionPolyfillReject__: true,\n              message,\n            });\n          }).catch(err => {\n            // Print an error on the console if unable to send the response.\n            console.error(\"Failed to send onMessage rejected reply\", err);\n          });\n        };\n\n        // If the listener returned a Promise, send the resolved value as a\n        // result, otherwise wait the promise related to the wrappedSendResponse\n        // callback to resolve and send it as a response.\n        if (isResultThenable) {\n          sendPromisedResult(result);\n        } else {\n          sendPromisedResult(sendResponsePromise);\n        }\n\n        // Let Chrome know that the listener is replying.\n        return true;\n      };\n    });\n\n    const wrappedSendMessageCallback = ({reject, resolve}, reply) => {\n      if (extensionAPIs.runtime.lastError) {\n        // Detect when none of the listeners replied to the sendMessage call and resolve\n        // the promise to undefined as in Firefox.\n        // See https://github.com/mozilla/webextension-polyfill/issues/130\n        if (extensionAPIs.runtime.lastError.message === CHROME_SEND_MESSAGE_CALLBACK_NO_RESPONSE_MESSAGE) {\n          resolve();\n        } else {\n          reject(new Error(extensionAPIs.runtime.lastError.message));\n        }\n      } else if (reply && reply.__mozWebExtensionPolyfillReject__) {\n        // Convert back the JSON representation of the error into\n        // an Error instance.\n        reject(new Error(reply.message));\n      } else {\n        resolve(reply);\n      }\n    };\n\n    const wrappedSendMessage = (name, metadata, apiNamespaceObj, ...args) => {\n      if (args.length < metadata.minArgs) {\n        throw new Error(`Expected at least ${metadata.minArgs} ${pluralizeArguments(metadata.minArgs)} for ${name}(), got ${args.length}`);\n      }\n\n      if (args.length > metadata.maxArgs) {\n        throw new Error(`Expected at most ${metadata.maxArgs} ${pluralizeArguments(metadata.maxArgs)} for ${name}(), got ${args.length}`);\n      }\n\n      return new Promise((resolve, reject) => {\n        const wrappedCb = wrappedSendMessageCallback.bind(null, {resolve, reject});\n        args.push(wrappedCb);\n        apiNamespaceObj.sendMessage(...args);\n      });\n    };\n\n    const staticWrappers = {\n      devtools: {\n        network: {\n          onRequestFinished: wrapEvent(onRequestFinishedWrappers),\n        },\n      },\n      runtime: {\n        onMessage: wrapEvent(onMessageWrappers),\n        onMessageExternal: wrapEvent(onMessageWrappers),\n        sendMessage: wrappedSendMessage.bind(null, \"sendMessage\", {minArgs: 1, maxArgs: 3}),\n      },\n      tabs: {\n        sendMessage: wrappedSendMessage.bind(null, \"sendMessage\", {minArgs: 2, maxArgs: 3}),\n      },\n    };\n    const settingMetadata = {\n      clear: {minArgs: 1, maxArgs: 1},\n      get: {minArgs: 1, maxArgs: 1},\n      set: {minArgs: 1, maxArgs: 1},\n    };\n    apiMetadata.privacy = {\n      network: {\"*\": settingMetadata},\n      services: {\"*\": settingMetadata},\n      websites: {\"*\": settingMetadata},\n    };\n\n    return wrapObject(extensionAPIs, staticWrappers, apiMetadata);\n  };\n\n  // The build process adds a UMD wrapper around this file, which makes the\n  // `module` variable available.\n  module.exports = wrapAPIs(chrome);\n} else {\n  module.exports = globalThis.browser;\n}\n"], "mappings": ";;;;;;;;;;;;;EAAA;;EACA;;EACA;;EACA;AACA;AACA;EACA;;EAEA,IAAI,CAACA,UAAU,CAACC,MAAX,EAAmBC,OAAnB,EAA4BC,EAAjC,EAAqC;IACnC,MAAM,IAAIC,KAAJ,CAAU,2DAAV,CAAN;EACD;;EAED,IAAI,OAAOJ,UAAU,CAACK,OAAlB,KAA8B,WAA9B,IAA6CC,MAAM,CAACC,cAAP,CAAsBP,UAAU,CAACK,OAAjC,MAA8CC,MAAM,CAACE,SAAtG,EAAiH;IAC/G,MAAMC,gDAAgD,GAAG,yDAAzD,CAD+G,CAG/G;IACA;IACA;IACA;IACA;;IACA,MAAMC,QAAQ,GAAGC,aAAa,IAAI;MAChC;MACA;MACA;MACA,MAAMC,WAAW,GAAG;QAClB,UAAU;UACR,SAAS;YAC<PERSON>,WAAW,CADJ;YAEP,WAAW;UAFJ,CADD;UAKR,YAAY;YACV,WAAW,CADD;YAEV,WAAW;UAFD,CALJ;UASR,OAAO;YACL,WAAW,CADN;YAEL,WAAW;UAFN,CATC;UAaR,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH;QAbF,CADQ;QAmBlB,aAAa;UACX,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CADC;UAKX,OAAO;YACL,WAAW,CADN;YAEL,WAAW;UAFN,CALI;UASX,eAAe;YACb,WAAW,CADE;YAEb,WAAW;UAFE,CATJ;UAaX,aAAa;YACX,WAAW,CADA;YAEX,WAAW;UAFA,CAbF;UAiBX,cAAc;YACZ,WAAW,CADC;YAEZ,WAAW;UAFC,CAjBH;UAqBX,WAAW;YACT,WAAW,CADF;YAET,WAAW;UAFF,CArBA;UAyBX,QAAQ;YACN,WAAW,CADL;YAEN,WAAW;UAFL,CAzBG;UA6BX,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CA7BC;UAiCX,cAAc;YACZ,WAAW,CADC;YAEZ,WAAW;UAFC,CAjCH;UAqCX,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CArCC;UAyCX,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH;QAzCC,CAnBK;QAiElB,iBAAiB;UACf,WAAW;YACT,WAAW,CADF;YAET,WAAW,CAFF;YAGT,wBAAwB;UAHf,CADI;UAMf,UAAU;YACR,WAAW,CADH;YAER,WAAW,CAFH;YAGR,wBAAwB;UAHhB,CANK;UAWf,2BAA2B;YACzB,WAAW,CADc;YAEzB,WAAW;UAFc,CAXZ;UAef,gBAAgB;YACd,WAAW,CADG;YAEd,WAAW;UAFG,CAfD;UAmBf,YAAY;YACV,WAAW,CADD;YAEV,WAAW;UAFD,CAnBG;UAuBf,YAAY;YACV,WAAW,CADD;YAEV,WAAW;UAFD,CAvBG;UA2Bf,aAAa;YACX,WAAW,CADA;YAEX,WAAW;UAFA,CA3BE;UA+Bf,2BAA2B;YACzB,WAAW,CADc;YAEzB,WAAW,CAFc;YAGzB,wBAAwB;UAHC,CA/BZ;UAoCf,gBAAgB;YACd,WAAW,CADG;YAEd,WAAW,CAFG;YAGd,wBAAwB;UAHV,CApCD;UAyCf,WAAW;YACT,WAAW,CADF;YAET,WAAW;UAFF,CAzCI;UA6Cf,YAAY;YACV,WAAW,CADD;YAEV,WAAW,CAFD;YAGV,wBAAwB;UAHd,CA7CG;UAkDf,YAAY;YACV,WAAW,CADD;YAEV,WAAW,CAFD;YAGV,wBAAwB;UAHd;QAlDG,CAjEC;QAyHlB,gBAAgB;UACd,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CADI;UAKd,eAAe;YACb,WAAW,CADE;YAEb,WAAW;UAFE,CALD;UASd,iBAAiB;YACf,WAAW,CADI;YAEf,WAAW;UAFI,CATH;UAad,mBAAmB;YACjB,WAAW,CADM;YAEjB,WAAW;UAFM,CAbL;UAiBd,kBAAkB;YAChB,WAAW,CADK;YAEhB,WAAW;UAFK,CAjBJ;UAqBd,iBAAiB;YACf,WAAW,CADI;YAEf,WAAW;UAFI,CArBH;UAyBd,sBAAsB;YACpB,WAAW,CADS;YAEpB,WAAW;UAFS,CAzBR;UA6Bd,mBAAmB;YACjB,WAAW,CADM;YAEjB,WAAW;UAFM,CA7BL;UAiCd,oBAAoB;YAClB,WAAW,CADO;YAElB,WAAW;UAFO,CAjCN;UAqCd,YAAY;YACV,WAAW,CADD;YAEV,WAAW;UAFD;QArCE,CAzHE;QAmKlB,YAAY;UACV,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH;QADA,CAnKM;QAyKlB,gBAAgB;UACd,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CADI;UAKd,aAAa;YACX,WAAW,CADA;YAEX,WAAW;UAFA,CALC;UASd,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH;QATI,CAzKE;QAuLlB,WAAW;UACT,OAAO;YACL,WAAW,CADN;YAEL,WAAW;UAFN,CADE;UAKT,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CALD;UAST,sBAAsB;YACpB,WAAW,CADS;YAEpB,WAAW;UAFS,CATb;UAaT,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CAbD;UAiBT,OAAO;YACL,WAAW,CADN;YAEL,WAAW;UAFN;QAjBE,CAvLO;QA6MlB,YAAY;UACV,mBAAmB;YACjB,QAAQ;cACN,WAAW,CADL;cAEN,WAAW,CAFL;cAGN,qBAAqB;YAHf;UADS,CADT;UAQV,UAAU;YACR,UAAU;cACR,WAAW,CADH;cAER,WAAW,CAFH;cAGR,qBAAqB;YAHb,CADF;YAMR,YAAY;cACV,qBAAqB;gBACnB,WAAW,CADQ;gBAEnB,WAAW;cAFQ;YADX;UANJ;QARA,CA7MM;QAmOlB,aAAa;UACX,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CADC;UAKX,YAAY;YACV,WAAW,CADD;YAEV,WAAW;UAFD,CALD;UASX,SAAS;YACP,WAAW,CADJ;YAEP,WAAW;UAFJ,CATE;UAaX,eAAe;YACb,WAAW,CADE;YAEb,WAAW;UAFE,CAbJ;UAiBX,QAAQ;YACN,WAAW,CADL;YAEN,WAAW,CAFL;YAGN,wBAAwB;UAHlB,CAjBG;UAsBX,SAAS;YACP,WAAW,CADJ;YAEP,WAAW;UAFJ,CAtBE;UA0BX,cAAc;YACZ,WAAW,CADC;YAEZ,WAAW;UAFC,CA1BH;UA8BX,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CA9BC;UAkCX,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CAlCC;UAsCX,QAAQ;YACN,WAAW,CADL;YAEN,WAAW,CAFL;YAGN,wBAAwB;UAHlB;QAtCG,CAnOK;QA+QlB,aAAa;UACX,6BAA6B;YAC3B,WAAW,CADgB;YAE3B,WAAW;UAFgB,CADlB;UAKX,4BAA4B;YAC1B,WAAW,CADe;YAE1B,WAAW;UAFe;QALjB,CA/QK;QAyRlB,WAAW;UACT,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CADD;UAKT,aAAa;YACX,WAAW,CADA;YAEX,WAAW;UAFA,CALJ;UAST,eAAe;YACb,WAAW,CADE;YAEb,WAAW;UAFE,CATN;UAaT,aAAa;YACX,WAAW,CADA;YAEX,WAAW;UAFA,CAbJ;UAiBT,aAAa;YACX,WAAW,CADA;YAEX,WAAW;UAFA,CAjBJ;UAqBT,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH;QArBD,CAzRO;QAmTlB,QAAQ;UACN,kBAAkB;YAChB,WAAW,CADK;YAEhB,WAAW;UAFK,CADZ;UAKN,sBAAsB;YACpB,WAAW,CADS;YAEpB,WAAW;UAFS;QALhB,CAnTU;QA6TlB,YAAY;UACV,qBAAqB;YACnB,WAAW,CADQ;YAEnB,WAAW;UAFQ;QADX,CA7TM;QAmUlB,QAAQ;UACN,cAAc;YACZ,WAAW,CADC;YAEZ,WAAW;UAFC;QADR,CAnUU;QAyUlB,cAAc;UACZ,OAAO;YACL,WAAW,CADN;YAEL,WAAW;UAFN,CADK;UAKZ,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CALE;UASZ,WAAW;YACT,WAAW,CADF;YAET,WAAW;UAFF,CATC;UAaZ,cAAc;YACZ,WAAW,CADC;YAEZ,WAAW;UAFC,CAbF;UAiBZ,iBAAiB;YACf,WAAW,CADI;YAEf,WAAW;UAFI;QAjBL,CAzUI;QA+VlB,iBAAiB;UACf,SAAS;YACP,WAAW,CADJ;YAEP,WAAW;UAFJ,CADM;UAKf,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CALK;UASf,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CATK;UAaf,sBAAsB;YACpB,WAAW,CADS;YAEpB,WAAW;UAFS,CAbP;UAiBf,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH;QAjBK,CA/VC;QAqXlB,cAAc;UACZ,YAAY;YACV,WAAW,CADD;YAEV,WAAW;UAFD,CADA;UAKZ,YAAY;YACV,WAAW,CADD;YAEV,WAAW;UAFD,CALA;UASZ,QAAQ;YACN,WAAW,CADL;YAEN,WAAW,CAFL;YAGN,wBAAwB;UAHlB,CATI;UAcZ,WAAW;YACT,WAAW,CADF;YAET,WAAW;UAFF,CAdC;UAkBZ,YAAY;YACV,WAAW,CADD;YAEV,WAAW,CAFD;YAGV,wBAAwB;UAHd,CAlBA;UAuBZ,YAAY;YACV,WAAW,CADD;YAEV,WAAW,CAFD;YAGV,wBAAwB;UAHd,CAvBA;UA4BZ,QAAQ;YACN,WAAW,CADL;YAEN,WAAW,CAFL;YAGN,wBAAwB;UAHlB;QA5BI,CArXI;QAuZlB,eAAe;UACb,YAAY;YACV,WAAW,CADD;YAEV,WAAW;UAFD,CADC;UAKb,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CALG;UASb,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CATG;UAab,WAAW;YACT,WAAW,CADF;YAET,WAAW;UAFF;QAbE,CAvZG;QAyalB,WAAW;UACT,qBAAqB;YACnB,WAAW,CADQ;YAEnB,WAAW;UAFQ,CADZ;UAKT,mBAAmB;YACjB,WAAW,CADM;YAEjB,WAAW;UAFM,CALV;UAST,mBAAmB;YACjB,WAAW,CADM;YAEjB,WAAW;UAFM,CATV;UAaT,sBAAsB;YACpB,WAAW,CADS;YAEpB,WAAW;UAFS,CAbb;UAiBT,eAAe;YACb,WAAW,CADE;YAEb,WAAW;UAFE,CAjBN;UAqBT,qBAAqB;YACnB,WAAW,CADQ;YAEnB,WAAW;UAFQ,CArBZ;UAyBT,mBAAmB;YACjB,WAAW,CADM;YAEjB,WAAW;UAFM;QAzBV,CAzaO;QAuclB,YAAY;UACV,cAAc;YACZ,WAAW,CADC;YAEZ,WAAW;UAFC,CADJ;UAKV,qBAAqB;YACnB,WAAW,CADQ;YAEnB,WAAW;UAFQ,CALX;UASV,WAAW;YACT,WAAW,CADF;YAET,WAAW;UAFF;QATD,CAvcM;QAqdlB,WAAW;UACT,SAAS;YACP,SAAS;cACP,WAAW,CADJ;cAEP,WAAW;YAFJ,CADF;YAKP,OAAO;cACL,WAAW,CADN;cAEL,WAAW;YAFN,CALA;YASP,iBAAiB;cACf,WAAW,CADI;cAEf,WAAW;YAFI,CATV;YAaP,UAAU;cACR,WAAW,CADH;cAER,WAAW;YAFH,CAbH;YAiBP,OAAO;cACL,WAAW,CADN;cAEL,WAAW;YAFN;UAjBA,CADA;UAuBT,WAAW;YACT,OAAO;cACL,WAAW,CADN;cAEL,WAAW;YAFN,CADE;YAKT,iBAAiB;cACf,WAAW,CADI;cAEf,WAAW;YAFI;UALR,CAvBF;UAiCT,QAAQ;YACN,SAAS;cACP,WAAW,CADJ;cAEP,WAAW;YAFJ,CADH;YAKN,OAAO;cACL,WAAW,CADN;cAEL,WAAW;YAFN,CALD;YASN,iBAAiB;cACf,WAAW,CADI;cAEf,WAAW;YAFI,CATX;YAaN,UAAU;cACR,WAAW,CADH;cAER,WAAW;YAFH,CAbJ;YAiBN,OAAO;cACL,WAAW,CADN;cAEL,WAAW;YAFN;UAjBD;QAjCC,CArdO;QA6gBlB,QAAQ;UACN,qBAAqB;YACnB,WAAW,CADQ;YAEnB,WAAW;UAFQ,CADf;UAKN,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CALJ;UASN,kBAAkB;YAChB,WAAW,CADK;YAEhB,WAAW;UAFK,CATZ;UAaN,WAAW;YACT,WAAW,CADF;YAET,WAAW;UAFF,CAbL;UAiBN,aAAa;YACX,WAAW,CADA;YAEX,WAAW;UAFA,CAjBP;UAqBN,iBAAiB;YACf,WAAW,CADI;YAEf,WAAW;UAFI,CArBX;UAyBN,OAAO;YACL,WAAW,CADN;YAEL,WAAW;UAFN,CAzBD;UA6BN,cAAc;YACZ,WAAW,CADC;YAEZ,WAAW;UAFC,CA7BR;UAiCN,WAAW;YACT,WAAW,CADF;YAET,WAAW;UAFF,CAjCL;UAqCN,mBAAmB;YACjB,WAAW,CADM;YAEjB,WAAW;UAFM,CArCb;UAyCN,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CAzCJ;UA6CN,aAAa;YACX,WAAW,CADA;YAEX,WAAW;UAFA,CA7CP;UAiDN,aAAa;YACX,WAAW,CADA;YAEX,WAAW;UAFA,CAjDP;UAqDN,aAAa;YACX,WAAW,CADA;YAEX,WAAW;UAFA,CArDP;UAyDN,QAAQ;YACN,WAAW,CADL;YAEN,WAAW;UAFL,CAzDF;UA6DN,SAAS;YACP,WAAW,CADJ;YAEP,WAAW;UAFJ,CA7DH;UAiEN,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CAjEJ;UAqEN,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CArEJ;UAyEN,aAAa;YACX,WAAW,CADA;YAEX,WAAW;UAFA,CAzEP;UA6EN,eAAe;YACb,WAAW,CADE;YAEb,WAAW;UAFE,CA7ET;UAiFN,WAAW;YACT,WAAW,CADF;YAET,WAAW;UAFF,CAjFL;UAqFN,mBAAmB;YACjB,WAAW,CADM;YAEjB,WAAW;UAFM,CArFb;UAyFN,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH;QAzFJ,CA7gBU;QA2mBlB,YAAY;UACV,OAAO;YACL,WAAW,CADN;YAEL,WAAW;UAFN;QADG,CA3mBM;QAinBlB,iBAAiB;UACf,gBAAgB;YACd,WAAW,CADG;YAEd,WAAW;UAFG,CADD;UAKf,YAAY;YACV,WAAW,CADD;YAEV,WAAW;UAFD;QALG,CAjnBC;QA2nBlB,cAAc;UACZ,0BAA0B;YACxB,WAAW,CADa;YAExB,WAAW;UAFa;QADd,CA3nBI;QAioBlB,WAAW;UACT,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CADD;UAKT,OAAO;YACL,WAAW,CADN;YAEL,WAAW;UAFN,CALE;UAST,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CATD;UAaT,cAAc;YACZ,WAAW,CADC;YAEZ,WAAW;UAFC,CAbL;UAiBT,kBAAkB;YAChB,WAAW,CADK;YAEhB,WAAW;UAFK,CAjBT;UAqBT,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH,CArBD;UAyBT,UAAU;YACR,WAAW,CADH;YAER,WAAW;UAFH;QAzBD;MAjoBO,CAApB;;MAiqBA,IAAIN,MAAM,CAACO,IAAP,CAAYD,WAAZ,EAAyBE,MAAzB,KAAoC,CAAxC,EAA2C;QACzC,MAAM,IAAIV,KAAJ,CAAU,6DAAV,CAAN;MACD;MAED;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;MACI,MAAMW,cAAN,SAA6BC,OAA7B,CAAqC;QACnCC,WAAW,CAACC,UAAD,EAAaC,KAAK,GAAGC,SAArB,EAAgC;UACzC,MAAMD,KAAN;UACA,KAAKD,UAAL,GAAkBA,UAAlB;QACD;;QAEDG,GAAG,CAACC,GAAD,EAAM;UACP,IAAI,CAAC,KAAKC,GAAL,CAASD,GAAT,CAAL,EAAoB;YAClB,KAAKE,GAAL,CAASF,GAAT,EAAc,KAAKJ,UAAL,CAAgBI,GAAhB,CAAd;UACD;;UAED,OAAO,MAAMD,GAAN,CAAUC,GAAV,CAAP;QACD;;MAZkC;MAerC;AACJ;AACA;AACA;AACA;AACA;AACA;;;MACI,MAAMG,UAAU,GAAGC,KAAK,IAAI;QAC1B,OAAOA,KAAK,IAAI,OAAOA,KAAP,KAAiB,QAA1B,IAAsC,OAAOA,KAAK,CAACC,IAAb,KAAsB,UAAnE;MACD,CAFD;MAIA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;MACI,MAAMC,YAAY,GAAG,CAACC,OAAD,EAAUC,QAAV,KAAuB;QAC1C,OAAO,CAAC,GAAGC,YAAJ,KAAqB;UAC1B,IAAIpB,aAAa,CAACT,OAAd,CAAsB8B,SAA1B,EAAqC;YACnCH,OAAO,CAACI,MAAR,CAAe,IAAI7B,KAAJ,CAAUO,aAAa,CAACT,OAAd,CAAsB8B,SAAtB,CAAgCE,OAA1C,CAAf;UACD,CAFD,MAEO,IAAIJ,QAAQ,CAACK,iBAAT,IACCJ,YAAY,CAACjB,MAAb,IAAuB,CAAvB,IAA4BgB,QAAQ,CAACK,iBAAT,KAA+B,KADhE,EACwE;YAC7EN,OAAO,CAACO,OAAR,CAAgBL,YAAY,CAAC,CAAD,CAA5B;UACD,CAHM,MAGA;YACLF,OAAO,CAACO,OAAR,CAAgBL,YAAhB;UACD;QACF,CATD;MAUD,CAXD;;MAaA,MAAMM,kBAAkB,GAAIC,OAAD,IAAaA,OAAO,IAAI,CAAX,GAAe,UAAf,GAA4B,WAApE;MAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;MACI,MAAMC,iBAAiB,GAAG,CAACC,IAAD,EAAOV,QAAP,KAAoB;QAC5C,OAAO,SAASW,oBAAT,CAA8BC,MAA9B,EAAsC,GAAGC,IAAzC,EAA+C;UACpD,IAAIA,IAAI,CAAC7B,MAAL,GAAcgB,QAAQ,CAACc,OAA3B,EAAoC;YAClC,MAAM,IAAIxC,KAAJ,CAAW,qBAAoB0B,QAAQ,CAACc,OAAQ,IAAGP,kBAAkB,CAACP,QAAQ,CAACc,OAAV,CAAmB,QAAOJ,IAAK,WAAUG,IAAI,CAAC7B,MAAO,EAA1H,CAAN;UACD;;UAED,IAAI6B,IAAI,CAAC7B,MAAL,GAAcgB,QAAQ,CAACe,OAA3B,EAAoC;YAClC,MAAM,IAAIzC,KAAJ,CAAW,oBAAmB0B,QAAQ,CAACe,OAAQ,IAAGR,kBAAkB,CAACP,QAAQ,CAACe,OAAV,CAAmB,QAAOL,IAAK,WAAUG,IAAI,CAAC7B,MAAO,EAAzH,CAAN;UACD;;UAED,OAAO,IAAIgC,OAAJ,CAAY,CAACV,OAAD,EAAUH,MAAV,KAAqB;YACtC,IAAIH,QAAQ,CAACiB,oBAAb,EAAmC;cACjC;cACA;cACA;cACA,IAAI;gBACFL,MAAM,CAACF,IAAD,CAAN,CAAa,GAAGG,IAAhB,EAAsBf,YAAY,CAAC;kBAACQ,OAAD;kBAAUH;gBAAV,CAAD,EAAoBH,QAApB,CAAlC;cACD,CAFD,CAEE,OAAOkB,OAAP,EAAgB;gBAChBC,OAAO,CAACC,IAAR,CAAc,GAAEV,IAAK,8DAAR,GACA,8CADb,EAC6DQ,OAD7D;gBAGAN,MAAM,CAACF,IAAD,CAAN,CAAa,GAAGG,IAAhB,EAJgB,CAMhB;gBACA;;gBACAb,QAAQ,CAACiB,oBAAT,GAAgC,KAAhC;gBACAjB,QAAQ,CAACqB,UAAT,GAAsB,IAAtB;gBAEAf,OAAO;cACR;YACF,CAnBD,MAmBO,IAAIN,QAAQ,CAACqB,UAAb,EAAyB;cAC9BT,MAAM,CAACF,IAAD,CAAN,CAAa,GAAGG,IAAhB;cACAP,OAAO;YACR,CAHM,MAGA;cACLM,MAAM,CAACF,IAAD,CAAN,CAAa,GAAGG,IAAhB,EAAsBf,YAAY,CAAC;gBAACQ,OAAD;gBAAUH;cAAV,CAAD,EAAoBH,QAApB,CAAlC;YACD;UACF,CA1BM,CAAP;QA2BD,CApCD;MAqCD,CAtCD;MAwCA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;MACI,MAAMsB,UAAU,GAAG,CAACV,MAAD,EAASW,MAAT,EAAiBC,OAAjB,KAA6B;QAC9C,OAAO,IAAIC,KAAJ,CAAUF,MAAV,EAAkB;UACvBG,KAAK,CAACC,YAAD,EAAeC,OAAf,EAAwBf,IAAxB,EAA8B;YACjC,OAAOW,OAAO,CAACK,IAAR,CAAaD,OAAb,EAAsBhB,MAAtB,EAA8B,GAAGC,IAAjC,CAAP;UACD;;QAHsB,CAAlB,CAAP;MAKD,CAND;;MAQA,IAAIiB,cAAc,GAAGC,QAAQ,CAACF,IAAT,CAAcG,IAAd,CAAmBxD,MAAM,CAACE,SAAP,CAAiBoD,cAApC,CAArB;MAEA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;MACI,MAAMG,UAAU,GAAG,CAACrB,MAAD,EAASsB,QAAQ,GAAG,EAApB,EAAwBlC,QAAQ,GAAG,EAAnC,KAA0C;QAC3D,IAAImC,KAAK,GAAG3D,MAAM,CAAC4D,MAAP,CAAc,IAAd,CAAZ;QACA,IAAIC,QAAQ,GAAG;UACb5C,GAAG,CAAC6C,WAAD,EAAcC,IAAd,EAAoB;YACrB,OAAOA,IAAI,IAAI3B,MAAR,IAAkB2B,IAAI,IAAIJ,KAAjC;UACD,CAHY;;UAKb5C,GAAG,CAAC+C,WAAD,EAAcC,IAAd,EAAoBC,QAApB,EAA8B;YAC/B,IAAID,IAAI,IAAIJ,KAAZ,EAAmB;cACjB,OAAOA,KAAK,CAACI,IAAD,CAAZ;YACD;;YAED,IAAI,EAAEA,IAAI,IAAI3B,MAAV,CAAJ,EAAuB;cACrB,OAAOtB,SAAP;YACD;;YAED,IAAIM,KAAK,GAAGgB,MAAM,CAAC2B,IAAD,CAAlB;;YAEA,IAAI,OAAO3C,KAAP,KAAiB,UAArB,EAAiC;cAC/B;cACA;cAEA,IAAI,OAAOsC,QAAQ,CAACK,IAAD,CAAf,KAA0B,UAA9B,EAA0C;gBACxC;gBACA3C,KAAK,GAAG0B,UAAU,CAACV,MAAD,EAASA,MAAM,CAAC2B,IAAD,CAAf,EAAuBL,QAAQ,CAACK,IAAD,CAA/B,CAAlB;cACD,CAHD,MAGO,IAAIT,cAAc,CAAC9B,QAAD,EAAWuC,IAAX,CAAlB,EAAoC;gBACzC;gBACA;gBACA,IAAIf,OAAO,GAAGf,iBAAiB,CAAC8B,IAAD,EAAOvC,QAAQ,CAACuC,IAAD,CAAf,CAA/B;gBACA3C,KAAK,GAAG0B,UAAU,CAACV,MAAD,EAASA,MAAM,CAAC2B,IAAD,CAAf,EAAuBf,OAAvB,CAAlB;cACD,CALM,MAKA;gBACL;gBACA;gBACA5B,KAAK,GAAGA,KAAK,CAACoC,IAAN,CAAWpB,MAAX,CAAR;cACD;YACF,CAjBD,MAiBO,IAAI,OAAOhB,KAAP,KAAiB,QAAjB,IAA6BA,KAAK,KAAK,IAAvC,KACCkC,cAAc,CAACI,QAAD,EAAWK,IAAX,CAAd,IACAT,cAAc,CAAC9B,QAAD,EAAWuC,IAAX,CAFf,CAAJ,EAEsC;cAC3C;cACA;cACA;cACA3C,KAAK,GAAGqC,UAAU,CAACrC,KAAD,EAAQsC,QAAQ,CAACK,IAAD,CAAhB,EAAwBvC,QAAQ,CAACuC,IAAD,CAAhC,CAAlB;YACD,CAPM,MAOA,IAAIT,cAAc,CAAC9B,QAAD,EAAW,GAAX,CAAlB,EAAmC;cACxC;cACAJ,KAAK,GAAGqC,UAAU,CAACrC,KAAD,EAAQsC,QAAQ,CAACK,IAAD,CAAhB,EAAwBvC,QAAQ,CAAC,GAAD,CAAhC,CAAlB;YACD,CAHM,MAGA;cACL;cACA;cACAxB,MAAM,CAACiE,cAAP,CAAsBN,KAAtB,EAA6BI,IAA7B,EAAmC;gBACjCG,YAAY,EAAE,IADmB;gBAEjCC,UAAU,EAAE,IAFqB;;gBAGjCpD,GAAG,GAAG;kBACJ,OAAOqB,MAAM,CAAC2B,IAAD,CAAb;gBACD,CALgC;;gBAMjC7C,GAAG,CAACE,KAAD,EAAQ;kBACTgB,MAAM,CAAC2B,IAAD,CAAN,GAAe3C,KAAf;gBACD;;cARgC,CAAnC;cAWA,OAAOA,KAAP;YACD;;YAEDuC,KAAK,CAACI,IAAD,CAAL,GAAc3C,KAAd;YACA,OAAOA,KAAP;UACD,CA9DY;;UAgEbF,GAAG,CAAC4C,WAAD,EAAcC,IAAd,EAAoB3C,KAApB,EAA2B4C,QAA3B,EAAqC;YACtC,IAAID,IAAI,IAAIJ,KAAZ,EAAmB;cACjBA,KAAK,CAACI,IAAD,CAAL,GAAc3C,KAAd;YACD,CAFD,MAEO;cACLgB,MAAM,CAAC2B,IAAD,CAAN,GAAe3C,KAAf;YACD;;YACD,OAAO,IAAP;UACD,CAvEY;;UAyEb6C,cAAc,CAACH,WAAD,EAAcC,IAAd,EAAoBK,IAApB,EAA0B;YACtC,OAAOC,OAAO,CAACJ,cAAR,CAAuBN,KAAvB,EAA8BI,IAA9B,EAAoCK,IAApC,CAAP;UACD,CA3EY;;UA6EbE,cAAc,CAACR,WAAD,EAAcC,IAAd,EAAoB;YAChC,OAAOM,OAAO,CAACC,cAAR,CAAuBX,KAAvB,EAA8BI,IAA9B,CAAP;UACD;;QA/EY,CAAf,CAF2D,CAoF3D;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;;QACA,IAAID,WAAW,GAAG9D,MAAM,CAAC4D,MAAP,CAAcxB,MAAd,CAAlB;QACA,OAAO,IAAIa,KAAJ,CAAUa,WAAV,EAAuBD,QAAvB,CAAP;MACD,CAhGD;MAkGA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;MACI,MAAMU,SAAS,GAAGC,UAAU,KAAK;QAC/BC,WAAW,CAACrC,MAAD,EAASsC,QAAT,EAAmB,GAAGrC,IAAtB,EAA4B;UACrCD,MAAM,CAACqC,WAAP,CAAmBD,UAAU,CAACzD,GAAX,CAAe2D,QAAf,CAAnB,EAA6C,GAAGrC,IAAhD;QACD,CAH8B;;QAK/BsC,WAAW,CAACvC,MAAD,EAASsC,QAAT,EAAmB;UAC5B,OAAOtC,MAAM,CAACuC,WAAP,CAAmBH,UAAU,CAACzD,GAAX,CAAe2D,QAAf,CAAnB,CAAP;QACD,CAP8B;;QAS/BE,cAAc,CAACxC,MAAD,EAASsC,QAAT,EAAmB;UAC/BtC,MAAM,CAACwC,cAAP,CAAsBJ,UAAU,CAACzD,GAAX,CAAe2D,QAAf,CAAtB;QACD;;MAX8B,CAAL,CAA5B;;MAcA,MAAMG,yBAAyB,GAAG,IAAIpE,cAAJ,CAAmBiE,QAAQ,IAAI;QAC/D,IAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;UAClC,OAAOA,QAAP;QACD;QAED;AACN;AACA;AACA;AACA;AACA;AACA;AACA;;;QACM,OAAO,SAASI,iBAAT,CAA2BC,GAA3B,EAAgC;UACrC,MAAMC,UAAU,GAAGvB,UAAU,CAACsB,GAAD,EAAM;UAAG;UAAT,EAAyB;YACpDE,UAAU,EAAE;cACV3C,OAAO,EAAE,CADC;cAEVC,OAAO,EAAE;YAFC;UADwC,CAAzB,CAA7B;UAMAmC,QAAQ,CAACM,UAAD,CAAR;QACD,CARD;MASD,CAtBiC,CAAlC;MAwBA,MAAME,iBAAiB,GAAG,IAAIzE,cAAJ,CAAmBiE,QAAQ,IAAI;QACvD,IAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;UAClC,OAAOA,QAAP;QACD;QAED;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;QACM,OAAO,SAASS,SAAT,CAAmBvD,OAAnB,EAA4BwD,MAA5B,EAAoCC,YAApC,EAAkD;UACvD,IAAIC,mBAAmB,GAAG,KAA1B;UAEA,IAAIC,mBAAJ;UACA,IAAIC,mBAAmB,GAAG,IAAIhD,OAAJ,CAAYV,OAAO,IAAI;YAC/CyD,mBAAmB,GAAG,UAASE,QAAT,EAAmB;cACvCH,mBAAmB,GAAG,IAAtB;cACAxD,OAAO,CAAC2D,QAAD,CAAP;YACD,CAHD;UAID,CALyB,CAA1B;UAOA,IAAIC,MAAJ;;UACA,IAAI;YACFA,MAAM,GAAGhB,QAAQ,CAAC9C,OAAD,EAAUwD,MAAV,EAAkBG,mBAAlB,CAAjB;UACD,CAFD,CAEE,OAAOI,GAAP,EAAY;YACZD,MAAM,GAAGlD,OAAO,CAACb,MAAR,CAAegE,GAAf,CAAT;UACD;;UAED,MAAMC,gBAAgB,GAAGF,MAAM,KAAK,IAAX,IAAmBvE,UAAU,CAACuE,MAAD,CAAtD,CAlBuD,CAoBvD;UACA;UACA;;UACA,IAAIA,MAAM,KAAK,IAAX,IAAmB,CAACE,gBAApB,IAAwC,CAACN,mBAA7C,EAAkE;YAChE,OAAO,KAAP;UACD,CAzBsD,CA2BvD;UACA;UACA;UACA;;;UACA,MAAMO,kBAAkB,GAAItE,OAAD,IAAa;YACtCA,OAAO,CAACF,IAAR,CAAayE,GAAG,IAAI;cAClB;cACAT,YAAY,CAACS,GAAD,CAAZ;YACD,CAHD,EAGGC,KAAK,IAAI;cACV;cACA;cACA,IAAInE,OAAJ;;cACA,IAAImE,KAAK,KAAKA,KAAK,YAAYjG,KAAjB,IACV,OAAOiG,KAAK,CAACnE,OAAb,KAAyB,QADpB,CAAT,EACwC;gBACtCA,OAAO,GAAGmE,KAAK,CAACnE,OAAhB;cACD,CAHD,MAGO;gBACLA,OAAO,GAAG,8BAAV;cACD;;cAEDyD,YAAY,CAAC;gBACXW,iCAAiC,EAAE,IADxB;gBAEXpE;cAFW,CAAD,CAAZ;YAID,CAlBD,EAkBGqE,KAlBH,CAkBSN,GAAG,IAAI;cACd;cACAhD,OAAO,CAACoD,KAAR,CAAc,yCAAd,EAAyDJ,GAAzD;YACD,CArBD;UAsBD,CAvBD,CA/BuD,CAwDvD;UACA;UACA;;;UACA,IAAIC,gBAAJ,EAAsB;YACpBC,kBAAkB,CAACH,MAAD,CAAlB;UACD,CAFD,MAEO;YACLG,kBAAkB,CAACL,mBAAD,CAAlB;UACD,CA/DsD,CAiEvD;;;UACA,OAAO,IAAP;QACD,CAnED;MAoED,CA1FyB,CAA1B;;MA4FA,MAAMU,0BAA0B,GAAG,CAAC;QAACvE,MAAD;QAASG;MAAT,CAAD,EAAoBqE,KAApB,KAA8B;QAC/D,IAAI9F,aAAa,CAACT,OAAd,CAAsB8B,SAA1B,EAAqC;UACnC;UACA;UACA;UACA,IAAIrB,aAAa,CAACT,OAAd,CAAsB8B,SAAtB,CAAgCE,OAAhC,KAA4CzB,gDAAhD,EAAkG;YAChG2B,OAAO;UACR,CAFD,MAEO;YACLH,MAAM,CAAC,IAAI7B,KAAJ,CAAUO,aAAa,CAACT,OAAd,CAAsB8B,SAAtB,CAAgCE,OAA1C,CAAD,CAAN;UACD;QACF,CATD,MASO,IAAIuE,KAAK,IAAIA,KAAK,CAACH,iCAAnB,EAAsD;UAC3D;UACA;UACArE,MAAM,CAAC,IAAI7B,KAAJ,CAAUqG,KAAK,CAACvE,OAAhB,CAAD,CAAN;QACD,CAJM,MAIA;UACLE,OAAO,CAACqE,KAAD,CAAP;QACD;MACF,CAjBD;;MAmBA,MAAMC,kBAAkB,GAAG,CAAClE,IAAD,EAAOV,QAAP,EAAiB6E,eAAjB,EAAkC,GAAGhE,IAArC,KAA8C;QACvE,IAAIA,IAAI,CAAC7B,MAAL,GAAcgB,QAAQ,CAACc,OAA3B,EAAoC;UAClC,MAAM,IAAIxC,KAAJ,CAAW,qBAAoB0B,QAAQ,CAACc,OAAQ,IAAGP,kBAAkB,CAACP,QAAQ,CAACc,OAAV,CAAmB,QAAOJ,IAAK,WAAUG,IAAI,CAAC7B,MAAO,EAA1H,CAAN;QACD;;QAED,IAAI6B,IAAI,CAAC7B,MAAL,GAAcgB,QAAQ,CAACe,OAA3B,EAAoC;UAClC,MAAM,IAAIzC,KAAJ,CAAW,oBAAmB0B,QAAQ,CAACe,OAAQ,IAAGR,kBAAkB,CAACP,QAAQ,CAACe,OAAV,CAAmB,QAAOL,IAAK,WAAUG,IAAI,CAAC7B,MAAO,EAAzH,CAAN;QACD;;QAED,OAAO,IAAIgC,OAAJ,CAAY,CAACV,OAAD,EAAUH,MAAV,KAAqB;UACtC,MAAM2E,SAAS,GAAGJ,0BAA0B,CAAC1C,IAA3B,CAAgC,IAAhC,EAAsC;YAAC1B,OAAD;YAAUH;UAAV,CAAtC,CAAlB;UACAU,IAAI,CAACkE,IAAL,CAAUD,SAAV;UACAD,eAAe,CAACG,WAAhB,CAA4B,GAAGnE,IAA/B;QACD,CAJM,CAAP;MAKD,CAdD;;MAgBA,MAAMoE,cAAc,GAAG;QACrBC,QAAQ,EAAE;UACRC,OAAO,EAAE;YACP7B,iBAAiB,EAAEP,SAAS,CAACM,yBAAD;UADrB;QADD,CADW;QAMrBjF,OAAO,EAAE;UACPuF,SAAS,EAAEZ,SAAS,CAACW,iBAAD,CADb;UAEP0B,iBAAiB,EAAErC,SAAS,CAACW,iBAAD,CAFrB;UAGPsB,WAAW,EAAEJ,kBAAkB,CAAC5C,IAAnB,CAAwB,IAAxB,EAA8B,aAA9B,EAA6C;YAAClB,OAAO,EAAE,CAAV;YAAaC,OAAO,EAAE;UAAtB,CAA7C;QAHN,CANY;QAWrBsE,IAAI,EAAE;UACJL,WAAW,EAAEJ,kBAAkB,CAAC5C,IAAnB,CAAwB,IAAxB,EAA8B,aAA9B,EAA6C;YAAClB,OAAO,EAAE,CAAV;YAAaC,OAAO,EAAE;UAAtB,CAA7C;QADT;MAXe,CAAvB;MAeA,MAAMuE,eAAe,GAAG;QACtBC,KAAK,EAAE;UAACzE,OAAO,EAAE,CAAV;UAAaC,OAAO,EAAE;QAAtB,CADe;QAEtBxB,GAAG,EAAE;UAACuB,OAAO,EAAE,CAAV;UAAaC,OAAO,EAAE;QAAtB,CAFiB;QAGtBrB,GAAG,EAAE;UAACoB,OAAO,EAAE,CAAV;UAAaC,OAAO,EAAE;QAAtB;MAHiB,CAAxB;MAKAjC,WAAW,CAAC0G,OAAZ,GAAsB;QACpBL,OAAO,EAAE;UAAC,KAAKG;QAAN,CADW;QAEpBG,QAAQ,EAAE;UAAC,KAAKH;QAAN,CAFU;QAGpBI,QAAQ,EAAE;UAAC,KAAKJ;QAAN;MAHU,CAAtB;MAMA,OAAOrD,UAAU,CAACpD,aAAD,EAAgBoG,cAAhB,EAAgCnG,WAAhC,CAAjB;IACD,CAnqCD,CAR+G,CA6qC/G;IACA;;;IACA6G,MAAM,CAACC,OAAP,GAAiBhH,QAAQ,CAACT,MAAD,CAAzB;EACD,CAhrCD,MAgrCO;IACLwH,MAAM,CAACC,OAAP,GAAiB1H,UAAU,CAACK,OAA5B;EACD"}